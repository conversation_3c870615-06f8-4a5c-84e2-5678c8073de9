#!/usr/bin/env python3
"""
Polynomial Regression Results Visualization

This script generates plots from experimental results comparing different
polynomial regression implementations across multiple datasets.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json
import re

# Configure matplotlib for nice-looking plots
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_palette("husl")

# Directory paths
PLOT_DIR = Path("plots")
RESULTS_DIR = Path("results")
PLOT_DIR.mkdir(exist_ok=True)

# Color mapping for different implementations
COLORS = {
    'Python (Manual Bounds)': '#2E86AB',
    'Python (Unconstrained)': '#A23B72',
    'Idris2 (Constrained)': '#F18F01',
    'Idris2 (Unconstrained)': '#C73E1D'
}

def load_experimental_results():
    """Load experimental results from JSON and text files."""
    results = []

    # Load Python results from JSON
    python_file = RESULTS_DIR / "python_results.json"
    if python_file.exists():
        with open(python_file, 'r') as f:
            python_data = json.load(f)
            for result in python_data:
                # Standardize implementation names and extract compliance rate
                impl_name = result['model_name']
                if 'Manual Bounds' in impl_name:
                    impl_name = 'Python (Manual Bounds)'
                    compliance_rate = result.get('bound_compliance_rate', 1.0)
                elif 'Unconstrained' in impl_name:
                    impl_name = 'Python (Unconstrained)'
                    compliance_rate = None
                else:
                    compliance_rate = None
                results.append({
                    'Implementation': impl_name,
                    'Dataset': result['dataset_name'],
                    'Test_RMSE': result['test_rmse'],
                    'Test_R2': result['test_r2'],
                    'Execution_Time': result['execution_time'],
                    'Memory_Usage': result['peak_memory_mb'],
                    'Max_Coefficient': result['max_coefficient'],
                    'Bounds_Compliant': result['bounds_compliant'],
                    'Bound_Compliance_Rate': compliance_rate,
                    'Converged': result['converged']
                })
    else:
        print("Warning: Python results file not found")
    
    # Load Idris unconstrained results from main results file
    unconstrained_file = RESULTS_DIR / "idris_unconstrained_results.txt"
    if unconstrained_file.exists():
        with open(unconstrained_file, 'r') as f:
            content = f.read()

            # Parse results for each dataset
            datasets = ['clean', 'outliers', 'pathological']
            for dataset in datasets:
                # Look for the pattern in the unconstrained results
                pattern = (f"Unconstrained Results for {dataset}:.*?"
                          f"Test RMSE:\\s*([\\d.-]+).*?"
                          f"Test R²:\\s*([\\d.-]+).*?"
                          f"Max \\|Coeff\\|:\\s*([\\d.-]+).*?"
                          f"Execution Time:\\s*([\\d.-]+)s.*?"
                          f"Memory Usage:\\s*([\\d.-]+)MB")

                match = re.search(pattern, content, re.DOTALL)
                if match:
                    results.append({
                        'Implementation': 'Idris2 (Unconstrained)',
                        'Dataset': dataset,
                        'Test_RMSE': float(match.group(1)),
                        'Test_R2': float(match.group(2)),
                        'Execution_Time': float(match.group(4)),
                        'Memory_Usage': float(match.group(5)),
                        'Max_Coefficient': float(match.group(3)),
                        'Bounds_Compliant': None,
                        'Bound_Compliance_Rate': None,
                        'Converged': True
                    })
    else:
        print("Warning: idris_unconstrained_results.txt not found")
    
    # Load Idris bounded (Spidr) results
    bounded_file = RESULTS_DIR / "idris_bounded_results.txt"
    if bounded_file.exists():
        with open(bounded_file, 'r') as f:
            content = f.read()

            # Parse results for each dataset
            datasets = ['clean', 'outliers', 'pathological']
            for dataset in datasets:
                # Look for the pattern in the bounded results
                pattern = (f"Processing {dataset} dataset.*?"
                          f"Test RMSE:\\s*([\\d.-]+).*?"
                          f"Test R²:\\s*([\\d.-]+).*?"
                          f"Execution Time:\\s*([\\d.-]+).*?"
                          f"Memory Usage:\\s*([\\d.-]+).*?"
                          f"Max \\|coefficient\\|:\\s*([\\d.-]+).*?"
                          f"Bound compliance rate:\\s*([\\d.-]+)%")

                match = re.search(pattern, content, re.DOTALL)
                if match:
                    compliance_rate = float(match.group(6)) / 100.0

                    results.append({
                        'Implementation': 'Idris2 (Constrained)',
                        'Dataset': dataset,
                        'Test_RMSE': float(match.group(1)),
                        'Test_R2': float(match.group(2)),
                        'Execution_Time': float(match.group(3)),
                        'Memory_Usage': float(match.group(4)),
                        'Max_Coefficient': float(match.group(5)),
                        'Bounds_Compliant': True,
                        'Bound_Compliance_Rate': compliance_rate,
                        'Converged': True
                    })
    else:
        print("Warning: Idris bounded results file not found")
    
    return pd.DataFrame(results)

def create_rmse_comparison_plot(df):
    """Create RMSE performance comparison across datasets."""
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    datasets = ['clean', 'outliers', 'pathological']

    for i, dataset in enumerate(datasets):
        data = df[df['Dataset'] == dataset].copy()

        # Filter extreme outliers for better readability
        max_rmse = 5.0 if dataset == 'clean' else (3.0 if dataset == 'outliers' else 2.0)
        data = data[data['Test_RMSE'] < max_rmse]

        # Create bar chart
        bar_colors = [COLORS.get(impl, '#666666') for impl in data['Implementation']]
        bars = axes[i].bar(range(len(data)), data['Test_RMSE'], color=bar_colors)

        # Formatting
        axes[i].set_title(f'{dataset.title()} Dataset\nRMSE Performance', fontweight='bold')
        if i == 0:
            axes[i].set_ylabel('Test RMSE')

        axes[i].set_xticks(range(len(data)))
        impl_labels = [impl.replace(' (', '\n(') for impl in data['Implementation']]
        axes[i].set_xticklabels(impl_labels, rotation=45, ha='right')
        axes[i].grid(True, alpha=0.3)

        # Add value labels on bars
        for bar, rmse in zip(bars, data['Test_RMSE']):
            height = bar.get_height()
            axes[i].text(bar.get_x() + bar.get_width()/2, height + 0.01,
                        f'{rmse:.3f}', ha='center', va='bottom', fontweight='bold')

    plt.tight_layout()
    plt.savefig(PLOT_DIR / 'rmse_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("RMSE comparison plot saved")

def create_r2_comparison_plot(df):
    """Create R-squared accuracy comparison across datasets."""
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    datasets = ['clean', 'outliers', 'pathological']

    for i, dataset in enumerate(datasets):
        data = df[df['Dataset'] == dataset].copy()

        # Filter extreme negative values for readability
        data = data[data['Test_R2'] > -10.0]

        # Create bar chart
        bar_colors = [COLORS.get(impl, '#666666') for impl in data['Implementation']]
        bars = axes[i].bar(range(len(data)), data['Test_R2'], color=bar_colors)

        # Formatting
        axes[i].set_title(f'{dataset.title()} Dataset\nR² Accuracy', fontweight='bold')
        if i == 0:
            axes[i].set_ylabel('Test R²')

        axes[i].set_xticks(range(len(data)))
        impl_labels = [impl.replace(' (', '\n(') for impl in data['Implementation']]
        axes[i].set_xticklabels(impl_labels, rotation=45, ha='right')
        axes[i].grid(True, alpha=0.3)

        # Add reference line at R² = 0
        axes[i].axhline(y=0, color='red', linestyle='--', alpha=0.5)

        # Add value labels on bars
        for bar, r2 in zip(bars, data['Test_R2']):
            height = bar.get_height()
            y_offset = 0.05 if r2 >= 0 else -0.15
            v_align = 'bottom' if r2 >= 0 else 'top'

            axes[i].text(bar.get_x() + bar.get_width()/2, height + y_offset,
                        f'{r2:.2f}', ha='center', va=v_align, fontweight='bold')

    plt.tight_layout()
    plt.savefig(PLOT_DIR / 'r2_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("R-squared comparison plot saved")

def create_bound_compliance_plot(df):
    """Create bound compliance visualization for constrained models only."""
    constrained_df = df[df['Bound_Compliance_Rate'].notna()].copy()

    if constrained_df.empty:
        print("Warning: No bound compliance data found")
        return

    fig, ax = plt.subplots(1, 1, figsize=(10, 6))

    # Set up grouped bar chart
    datasets = constrained_df['Dataset'].unique()
    implementations = constrained_df['Implementation'].unique()

    x = np.arange(len(datasets))
    width = 0.35

    # Create bars for each implementation
    for i, impl in enumerate(implementations):
        impl_data = constrained_df[constrained_df['Implementation'] == impl]
        compliance_rates = []

        for dataset in datasets:
            dataset_data = impl_data[impl_data['Dataset'] == dataset]
            if not dataset_data.empty:
                rate = dataset_data['Bound_Compliance_Rate'].iloc[0] * 100
            else:
                rate = 0
            compliance_rates.append(rate)

        bars = ax.bar(x + i * width, compliance_rates, width,
                     label=impl, color=COLORS.get(impl, '#666666'))

        # Add value labels on bars
        for bar, rate in zip(bars, compliance_rates):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2, height + 1,
                   f'{rate:.1f}%', ha='center', va='bottom', fontweight='bold')

    # Chart formatting
    ax.set_title('Bound Compliance Rate\n(Constrained Models Only)', fontweight='bold', fontsize=14)
    ax.set_ylabel('Compliance Rate (%)')
    ax.set_xlabel('Dataset')
    ax.set_xticks(x + width/2)
    ax.set_xticklabels([d.title() for d in datasets])
    ax.set_ylim(0, 110)
    ax.legend()
    ax.grid(True, alpha=0.3)

    # Add reference line for perfect compliance
    ax.axhline(y=100, color='green', linestyle='--', alpha=0.7)

    plt.tight_layout()
    plt.savefig(PLOT_DIR / 'bound_compliance.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("Bound compliance plot saved")

def create_performance_tradeoff_plot(df):
    """Create performance vs efficiency trade-off visualization."""
    fig, axes = plt.subplots(1, 2, figsize=(15, 6))

    # Left plot: RMSE vs Execution Time
    for impl in df['Implementation'].unique():
        impl_data = df[df['Implementation'] == impl]
        # Filter extreme outliers for better readability
        impl_data = impl_data[impl_data['Test_RMSE'] < 5.0]

        color = COLORS.get(impl, '#666666')
        axes[0].scatter(impl_data['Execution_Time'], impl_data['Test_RMSE'],
                       s=100, alpha=0.7, label=impl, color=color)

    axes[0].set_xlabel('Execution Time (seconds)')
    axes[0].set_ylabel('Test RMSE')
    axes[0].set_title('Accuracy vs Speed Trade-off', fontweight='bold')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    axes[0].set_xscale('log')

    # Right plot: RMSE vs Memory Usage
    for impl in df['Implementation'].unique():
        impl_data = df[df['Implementation'] == impl]
        # Filter extreme outliers for better readability
        impl_data = impl_data[impl_data['Test_RMSE'] < 5.0]

        color = COLORS.get(impl, '#666666')
        axes[1].scatter(impl_data['Memory_Usage'], impl_data['Test_RMSE'],
                       s=100, alpha=0.7, label=impl, color=color)

    axes[1].set_xlabel('Memory Usage (MB)')
    axes[1].set_ylabel('Test RMSE')
    axes[1].set_title('Accuracy vs Memory Trade-off', fontweight='bold')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(PLOT_DIR / 'performance_tradeoff.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("Performance trade-off plot saved")

def create_execution_time_comparison(df):
    """Create execution time comparison across implementations."""
    fig, ax = plt.subplots(1, 1, figsize=(12, 6))

    # Calculate mean and standard deviation for each implementation
    time_stats = df.groupby('Implementation')['Execution_Time'].agg(['mean', 'std']).reset_index()

    # Create bar chart with error bars
    bar_colors = [COLORS.get(impl, '#666666') for impl in time_stats['Implementation']]
    bars = ax.bar(range(len(time_stats)), time_stats['mean'],
                  yerr=time_stats['std'], capsize=5, color=bar_colors)

    # Chart formatting
    ax.set_title('Execution Time Comparison\n(Average across all datasets)', fontweight='bold', fontsize=14)
    ax.set_ylabel('Execution Time (seconds)')
    ax.set_xticks(range(len(time_stats)))

    impl_labels = [impl.replace(' (', '\n(') for impl in time_stats['Implementation']]
    ax.set_xticklabels(impl_labels, rotation=45, ha='right')
    ax.set_yscale('log')
    ax.grid(True, alpha=0.3)

    # Add value labels on bars
    for bar, mean_time in zip(bars, time_stats['mean']):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2, height * 1.1,
               f'{mean_time:.3f}s', ha='center', va='bottom', fontweight='bold')

    plt.tight_layout()
    plt.savefig(PLOT_DIR / 'execution_time_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("Execution time comparison plot saved")

def create_comprehensive_summary_plot(df):
    """Create a 4-panel summary visualization."""
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))

    # Top left: RMSE performance on clean dataset
    clean_data = df[df['Dataset'] == 'clean'].copy()
    clean_data = clean_data[clean_data['Test_RMSE'] < 1.0]  # Filter outliers

    bar_colors = [COLORS.get(impl, '#666666') for impl in clean_data['Implementation']]
    bars1 = axes[0,0].bar(range(len(clean_data)), clean_data['Test_RMSE'], color=bar_colors)

    axes[0,0].set_title('Clean Dataset: RMSE Performance', fontweight='bold')
    axes[0,0].set_ylabel('Test RMSE')
    axes[0,0].set_xticks(range(len(clean_data)))

    impl_labels = [impl.replace(' (', '\n(') for impl in clean_data['Implementation']]
    axes[0,0].set_xticklabels(impl_labels, rotation=45, ha='right')
    axes[0,0].grid(True, alpha=0.3)

    # Add value labels on bars
    for bar, rmse in zip(bars1, clean_data['Test_RMSE']):
        height = bar.get_height()
        axes[0,0].text(bar.get_x() + bar.get_width()/2, height + 0.005,
                      f'{rmse:.3f}', ha='center', va='bottom', fontweight='bold', fontsize=10)

    # Panel 2: Bound Compliance (Constrained models only)
    constrained_df = df[df['Bound_Compliance_Rate'].notna()].copy()
    if not constrained_df.empty:
        compliance_summary = constrained_df.groupby('Implementation')['Bound_Compliance_Rate'].mean() * 100
        bars2 = axes[0,1].bar(range(len(compliance_summary)), compliance_summary.values,
                             color=[COLORS.get(impl, '#666666') for impl in compliance_summary.index])
        axes[0,1].set_title('Bound Compliance Rate\n(Average across datasets)', fontweight='bold')
        axes[0,1].set_ylabel('Compliance Rate (%)')
        axes[0,1].set_xticks(range(len(compliance_summary)))
        axes[0,1].set_xticklabels([impl.replace(' (', '\n(') for impl in compliance_summary.index],
                                 rotation=45, ha='right')
        axes[0,1].set_ylim(0, 110)
        axes[0,1].grid(True, alpha=0.3)
        axes[0,1].axhline(y=100, color='green', linestyle='--', alpha=0.7)

        # Add value labels
        for bar, rate in zip(bars2, compliance_summary.values):
            axes[0,1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 2,
                          f'{rate:.1f}%', ha='center', va='bottom', fontweight='bold', fontsize=10)

    # Panel 3: Execution Time (Log scale)
    time_summary = df.groupby('Implementation')['Execution_Time'].mean()
    bars3 = axes[1,0].bar(range(len(time_summary)), time_summary.values,
                         color=[COLORS.get(impl, '#666666') for impl in time_summary.index])
    axes[1,0].set_title('Average Execution Time', fontweight='bold')
    axes[1,0].set_ylabel('Execution Time (seconds)')
    axes[1,0].set_xticks(range(len(time_summary)))
    axes[1,0].set_xticklabels([impl.replace(' (', '\n(') for impl in time_summary.index],
                             rotation=45, ha='right')
    axes[1,0].set_yscale('log')
    axes[1,0].grid(True, alpha=0.3)

    # Add value labels
    for bar, time_val in zip(bars3, time_summary.values):
        axes[1,0].text(bar.get_x() + bar.get_width()/2, bar.get_height() * 1.2,
                      f'{time_val:.3f}s', ha='center', va='bottom', fontweight='bold', fontsize=10)

    # Panel 4: Memory Usage
    memory_summary = df.groupby('Implementation')['Memory_Usage'].mean()
    bars4 = axes[1,1].bar(range(len(memory_summary)), memory_summary.values,
                         color=[COLORS.get(impl, '#666666') for impl in memory_summary.index])
    axes[1,1].set_title('Average Memory Usage', fontweight='bold')
    axes[1,1].set_ylabel('Memory Usage (MB)')
    axes[1,1].set_xticks(range(len(memory_summary)))
    axes[1,1].set_xticklabels([impl.replace(' (', '\n(') for impl in memory_summary.index],
                             rotation=45, ha='right')
    axes[1,1].grid(True, alpha=0.3)

    # Add value labels
    for bar, mem_val in zip(bars4, memory_summary.values):
        axes[1,1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.05,
                      f'{mem_val:.2f}MB', ha='center', va='bottom', fontweight='bold', fontsize=10)

    plt.suptitle('Polynomial Regression: Comprehensive Performance Analysis',
                 fontsize=16, fontweight='bold', y=0.98)
    plt.tight_layout()
    plt.subplots_adjust(top=0.93)
    plt.savefig(PLOT_DIR / 'comprehensive_summary.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("Comprehensive summary plot saved")

def create_constrained_vs_unconstrained_comparison(df):
    """Create separate comparisons for constrained vs unconstrained models."""
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))

    # Filter data for constrained and unconstrained models
    constrained_df = df[df['Implementation'].str.contains('Constrained|Manual Bounds')].copy()
    unconstrained_df = df[df['Implementation'].str.contains('Unconstrained')].copy()

    datasets = ['clean', 'outliers', 'pathological']

    # Top row: RMSE comparison
    # Constrained models RMSE
    constrained_rmse = []
    constrained_labels = []
    for dataset in datasets:
        for impl in constrained_df['Implementation'].unique():
            data = constrained_df[(constrained_df['Dataset'] == dataset) &
                                (constrained_df['Implementation'] == impl)]
            if not data.empty and data['Test_RMSE'].iloc[0] < 10.0:  # Filter extreme outliers
                constrained_rmse.append(data['Test_RMSE'].iloc[0])
                constrained_labels.append(f"{impl.split(' (')[0]}\n{dataset}")

    bars1 = axes[0,0].bar(range(len(constrained_rmse)), constrained_rmse,
                         color=['#2E86AB' if 'Python' in label else '#F18F01' for label in constrained_labels])
    axes[0,0].set_title('Constrained Models: RMSE Performance', fontweight='bold')
    axes[0,0].set_ylabel('Test RMSE')
    axes[0,0].set_xticks(range(len(constrained_labels)))
    axes[0,0].set_xticklabels(constrained_labels, rotation=45, ha='right')
    axes[0,0].grid(True, alpha=0.3)

    # Add value labels
    for bar, rmse in zip(bars1, constrained_rmse):
        axes[0,0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                      f'{rmse:.3f}', ha='center', va='bottom', fontweight='bold', fontsize=9)

    # Unconstrained models RMSE
    unconstrained_rmse = []
    unconstrained_labels = []
    for dataset in datasets:
        for impl in unconstrained_df['Implementation'].unique():
            data = unconstrained_df[(unconstrained_df['Dataset'] == dataset) &
                                  (unconstrained_df['Implementation'] == impl)]
            if not data.empty:
                unconstrained_rmse.append(data['Test_RMSE'].iloc[0])
                unconstrained_labels.append(f"{impl.split(' (')[0]}\n{dataset}")

    bars2 = axes[0,1].bar(range(len(unconstrained_rmse)), unconstrained_rmse,
                         color=['#A23B72' if 'Python' in label else '#C73E1D' for label in unconstrained_labels])
    axes[0,1].set_title('Unconstrained Models: RMSE Performance', fontweight='bold')
    axes[0,1].set_ylabel('Test RMSE')
    axes[0,1].set_xticks(range(len(unconstrained_labels)))
    axes[0,1].set_xticklabels(unconstrained_labels, rotation=45, ha='right')
    axes[0,1].grid(True, alpha=0.3)

    # Add value labels
    for bar, rmse in zip(bars2, unconstrained_rmse):
        axes[0,1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                      f'{rmse:.3f}', ha='center', va='bottom', fontweight='bold', fontsize=9)

    # Bottom row: Execution time comparison
    # Constrained models execution time
    constrained_time = []
    for dataset in datasets:
        for impl in constrained_df['Implementation'].unique():
            data = constrained_df[(constrained_df['Dataset'] == dataset) &
                                (constrained_df['Implementation'] == impl)]
            if not data.empty:
                constrained_time.append(data['Execution_Time'].iloc[0])

    bars3 = axes[1,0].bar(range(len(constrained_time)), constrained_time,
                         color=['#2E86AB' if 'Python' in label else '#F18F01' for label in constrained_labels])
    axes[1,0].set_title('Constrained Models: Execution Time', fontweight='bold')
    axes[1,0].set_ylabel('Execution Time (seconds)')
    axes[1,0].set_xticks(range(len(constrained_labels)))
    axes[1,0].set_xticklabels(constrained_labels, rotation=45, ha='right')
    axes[1,0].set_yscale('log')
    axes[1,0].grid(True, alpha=0.3)

    # Unconstrained models execution time
    unconstrained_time = []
    for dataset in datasets:
        for impl in unconstrained_df['Implementation'].unique():
            data = unconstrained_df[(unconstrained_df['Dataset'] == dataset) &
                                  (unconstrained_df['Implementation'] == impl)]
            if not data.empty:
                unconstrained_time.append(data['Execution_Time'].iloc[0])

    bars4 = axes[1,1].bar(range(len(unconstrained_time)), unconstrained_time,
                         color=['#A23B72' if 'Python' in label else '#C73E1D' for label in unconstrained_labels])
    axes[1,1].set_title('Unconstrained Models: Execution Time', fontweight='bold')
    axes[1,1].set_ylabel('Execution Time (seconds)')
    axes[1,1].set_xticks(range(len(unconstrained_labels)))
    axes[1,1].set_xticklabels(unconstrained_labels, rotation=45, ha='right')
    axes[1,1].set_yscale('log')
    axes[1,1].grid(True, alpha=0.3)

    plt.suptitle('Constrained vs Unconstrained Model Comparison', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.subplots_adjust(top=0.93)
    plt.savefig(PLOT_DIR / 'constrained_vs_unconstrained.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("Constrained vs unconstrained comparison plot saved")

if __name__ == "__main__":
    print("Generating experimental plots...")

    # Load experimental results
    df = load_experimental_results()
    print(f"Loaded {len(df)} experimental results")

    if df.empty:
        print("Error: No experimental results found!")
        exit(1)

    # Generate all visualization plots
    create_rmse_comparison_plot(df)
    create_r2_comparison_plot(df)
    create_bound_compliance_plot(df)
    create_performance_tradeoff_plot(df)
    create_execution_time_comparison(df)
    create_constrained_vs_unconstrained_comparison(df)
    create_comprehensive_summary_plot(df)

    print(f"All plots generated successfully in {PLOT_DIR}/")
    print("Generated plots:")
    for plot_file in sorted(PLOT_DIR.glob("*.png")):
        print(f"  - {plot_file.name}")
