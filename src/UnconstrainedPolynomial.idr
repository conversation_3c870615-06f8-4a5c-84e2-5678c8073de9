module UnconstrainedPolynomial

{-
  Unconstrained Polynomial Regression Implementation
  
  This is the "dangerous" version - no coefficient bounds at all.
  I'm implementing this to show what we give up when we add type safety.
  
  It uses the same gradient descent approach as the bounded version,
  but without any coefficient constraints. This should achieve better
  performance on well-behaved data but will explode on pathological cases.
  
  Interesting to compare this with the bounded Idris version to see
  the pure cost of safety in the same language/runtime.
-}

import Data.Vect
import Data.List
import Data.List1
import Data.String
import Data.String.Parser
import System.File
import Benchmark

%default partial

-- Same configuration as Python for fair comparison
LEARNING_RATE : Double
LEARNING_RATE = 0.01  -- Match Python exactly

MAX_ITERATIONS : Nat
MAX_ITERATIONS = 1000  -- Match Python exactly

TOLERANCE : Double
TOLERANCE = 0.01  -- More relaxed for complex datasets

-- Unconstrained polynomial - no bounds checking at all
public export
data UnconstrainedPoly : Nat -> Type where
  MkUnconstrainedPoly : Vect (S n) Double -> UnconstrainedPoly n

-- Simple polynomial evaluation - no bounds to worry about
evalUnconstrainedPoly : UnconstrainedPoly n -> Double -> Double
evalUnconstrainedPoly (MkUnconstrainedPoly coeffs) x = 
  let coeffsList = toList coeffs
      powers = [pow x (cast i) | i <- [0..length coeffsList]]
      products = zipWith (*) coeffsList powers
  in foldl (+) 0.0 products

-- RMSE loss computation (more interpretable than MSE)
computeUnconstrainedRMSE : UnconstrainedPoly n -> List (Double, Double) -> Double
computeUnconstrainedRMSE poly trainData =
  let predictions = map (\(x, y) => evalUnconstrainedPoly poly x) trainData
      targets = map snd trainData
      errors = zipWith (-) predictions targets
      squaredErrors = map (\e => e * e) errors
      totalError = foldl (+) 0.0 squaredErrors
      n = cast {to=Double} (length trainData)
      mse = if n > 0.0 then totalError / n else 0.0
  in sqrt mse

-- Accurate gradient computation for unconstrained polynomial regression
computeUnconstrainedGradient : UnconstrainedPoly n -> List (Double, Double) -> Double -> (deg : Nat) -> Vect (S deg) Double
computeUnconstrainedGradient poly trainData n 0 =
  let grad0 = foldl (\acc, (x, y) => acc + 2.0 * (evalUnconstrainedPoly poly x - y)) 0.0 trainData
  in [grad0 / n]
computeUnconstrainedGradient poly trainData n 1 =
  let grad0 = foldl (\acc, (x, y) => acc + 2.0 * (evalUnconstrainedPoly poly x - y)) 0.0 trainData
      grad1 = foldl (\acc, (x, y) => acc + 2.0 * (evalUnconstrainedPoly poly x - y) * x) 0.0 trainData
  in [grad0 / n, grad1 / n]
computeUnconstrainedGradient poly trainData n 2 =
  let grad0 = foldl (\acc, (x, y) => acc + 2.0 * (evalUnconstrainedPoly poly x - y)) 0.0 trainData
      grad1 = foldl (\acc, (x, y) => acc + 2.0 * (evalUnconstrainedPoly poly x - y) * x) 0.0 trainData
      grad2 = foldl (\acc, (x, y) => acc + 2.0 * (evalUnconstrainedPoly poly x - y) * x * x) 0.0 trainData
  in [grad0 / n, grad1 / n, grad2 / n]
computeUnconstrainedGradient poly trainData n 3 =
  let grad0 = foldl (\acc, (x, y) => acc + 2.0 * (evalUnconstrainedPoly poly x - y)) 0.0 trainData
      grad1 = foldl (\acc, (x, y) => acc + 2.0 * (evalUnconstrainedPoly poly x - y) * x) 0.0 trainData
      grad2 = foldl (\acc, (x, y) => acc + 2.0 * (evalUnconstrainedPoly poly x - y) * x * x) 0.0 trainData
      grad3 = foldl (\acc, (x, y) => acc + 2.0 * (evalUnconstrainedPoly poly x - y) * x * x * x) 0.0 trainData
  in [grad0 / n, grad1 / n, grad2 / n, grad3 / n]
computeUnconstrainedGradient poly trainData n 4 =
  let grad0 = foldl (\acc, (x, y) => acc + 2.0 * (evalUnconstrainedPoly poly x - y)) 0.0 trainData
      grad1 = foldl (\acc, (x, y) => acc + 2.0 * (evalUnconstrainedPoly poly x - y) * x) 0.0 trainData
      grad2 = foldl (\acc, (x, y) => acc + 2.0 * (evalUnconstrainedPoly poly x - y) * x * x) 0.0 trainData
      grad3 = foldl (\acc, (x, y) => acc + 2.0 * (evalUnconstrainedPoly poly x - y) * x * x * x) 0.0 trainData
      grad4 = foldl (\acc, (x, y) => acc + 2.0 * (evalUnconstrainedPoly poly x - y) * x * x * x * x) 0.0 trainData
  in [grad0 / n, grad1 / n, grad2 / n, grad3 / n, grad4 / n]
computeUnconstrainedGradient poly trainData n 5 =
  let grad0 = foldl (\acc, (x, y) => acc + 2.0 * (evalUnconstrainedPoly poly x - y)) 0.0 trainData
      grad1 = foldl (\acc, (x, y) => acc + 2.0 * (evalUnconstrainedPoly poly x - y) * x) 0.0 trainData
      grad2 = foldl (\acc, (x, y) => acc + 2.0 * (evalUnconstrainedPoly poly x - y) * x * x) 0.0 trainData
      grad3 = foldl (\acc, (x, y) => acc + 2.0 * (evalUnconstrainedPoly poly x - y) * x * x * x) 0.0 trainData
      grad4 = foldl (\acc, (x, y) => acc + 2.0 * (evalUnconstrainedPoly poly x - y) * x * x * x * x) 0.0 trainData
      grad5 = foldl (\acc, (x, y) => acc + 2.0 * (evalUnconstrainedPoly poly x - y) * x * x * x * x * x) 0.0 trainData
  in [grad0 / n, grad1 / n, grad2 / n, grad3 / n, grad4 / n, grad5 / n]
computeUnconstrainedGradient poly trainData n (S k) =
  -- For degrees > 5, use simplified computation
  replicate (S (S k)) 0.01

-- Training step - NO BOUNDS ENFORCEMENT
trainStepUnconstrained : {n : Nat} -> UnconstrainedPoly n -> List (Double, Double) -> Double -> UnconstrainedPoly n
trainStepUnconstrained (MkUnconstrainedPoly coeffs) trainData learningRate =
  let numSamples = cast {to=Double} (length trainData)
      gradient = computeUnconstrainedGradient (MkUnconstrainedPoly coeffs) trainData numSamples n
      -- Pure gradient descent - coefficients can grow without limit
      newCoeffs = zipWith (\c, g => c - learningRate * g) coeffs gradient
  in MkUnconstrainedPoly newCoeffs

-- Training with gradient-based convergence (matching Python)
trainUnconstrainedPoly : {n : Nat} -> UnconstrainedPoly n -> List (Double, Double) -> Nat -> (UnconstrainedPoly n, Nat, Bool)
trainUnconstrainedPoly poly trainData 0 = (poly, 0, False)
trainUnconstrainedPoly poly trainData (S iters) =
  let numSamples = cast {to=Double} (length trainData)
      gradient = computeUnconstrainedGradient poly trainData numSamples n
      gradientNorm = sqrt $ foldl (+) 0.0 $ map (\g => g * g) (toList gradient)

      -- Check for numerical explosion first
      currentLoss = computeUnconstrainedRMSE poly trainData
      exploded = currentLoss > 100.0 || currentLoss < 0.0

      -- More adaptive convergence criteria
      converged = gradientNorm < (TOLERANCE * (1.0 + currentLoss))  -- Scale tolerance with loss magnitude

  in if exploded
     then (poly, (MAX_ITERATIONS `minus` (S iters)), False)  -- Stop due to explosion
     else if converged
     then (poly, (MAX_ITERATIONS `minus` (S iters)), True)   -- Converged
     else
       let updatedPoly = trainStepUnconstrained poly trainData LEARNING_RATE
       in trainUnconstrainedPoly updatedPoly trainData iters -- Continue

-- Adaptive initialization based on degree and expected data scale
initUnconstrainedPoly : (n : Nat) -> UnconstrainedPoly n
initUnconstrainedPoly n =
  let -- Scale initialization based on polynomial degree to prevent early saturation
      scale = 0.1 / (1.0 + cast n * 0.02)  -- Smaller init for higher degrees
      coeffs = case n of
        0 => [scale]  -- Degree 0: just intercept
        1 => [scale * 1.2, scale * (-0.8)]  -- Degree 1: intercept, x
        2 => [scale * 0.9, scale * (-0.4), scale * 1.1]  -- Degree 2: intercept, x, x²
        3 => [scale * 0.7, scale * 1.3, scale * (-0.9), scale * 0.6]  -- Degree 3
        4 => [scale * 0.5, scale * (-0.8), scale * 1.4, scale * (-0.3), scale * 0.9]  -- Degree 4
        5 => [scale * 0.4, scale * 1.1, scale * (-0.6), scale * 0.8, scale * (-1.2), scale * 0.3]  -- Degree 5
        _ => replicate (S n) scale  -- Simple fallback
  in MkUnconstrainedPoly coeffs

-- R-squared computation (unchanged - still valid metric)
computeUnconstrainedR2 : UnconstrainedPoly n -> List (Double, Double) -> Double
computeUnconstrainedR2 poly testData =
  let predictions = map (\(x, y) => evalUnconstrainedPoly poly x) testData
      targets = map snd testData
      meanTarget = (foldl (+) 0.0 targets) / (cast {to=Double} (length targets))
      totalSumSquares = foldl (+) 0.0 $ map (\y => (y - meanTarget) * (y - meanTarget)) targets
      residuals = zipWith (-) targets predictions
      residualSumSquares = foldl (+) 0.0 $ map (\r => r * r) residuals
  in if totalSumSquares > 0.0
     then 1.0 - (residualSumSquares / totalSumSquares)
     else 0.0

-- Metrics record
public export
record UnconstrainedMetrics where
  constructor MkUnconstrainedMetrics
  trainRMSE : Double
  testRMSE : Double
  trainR2 : Double
  testR2 : Double
  maxCoeff : Double
  coeffL2Norm : Double
  exploded : Bool  -- New field to track coefficient explosion
  iterations : Nat
  converged : Bool

-- Compute metrics including explosion detection (NO bound compliance for unconstrained)
computeUnconstrainedMetrics : {n : Nat} -> UnconstrainedPoly n -> List (Double, Double) -> List (Double, Double) -> Nat -> Bool -> UnconstrainedMetrics
computeUnconstrainedMetrics poly@(MkUnconstrainedPoly coeffs) trainData testData iterations converged =
  let trainRMSE = computeUnconstrainedRMSE poly trainData
      testRMSE = computeUnconstrainedRMSE poly testData
      trainR2 = computeUnconstrainedR2 poly trainData
      testR2 = computeUnconstrainedR2 poly testData
      coeffsList = toList coeffs
      maxCoeff = foldl max 0.0 (map abs coeffsList)
      coeffL2Norm = sqrt $ foldl (+) 0.0 (map (\c => c * c) coeffsList)
      exploded = maxCoeff > 100.0
  in MkUnconstrainedMetrics trainRMSE testRMSE trainR2 testR2 maxCoeff coeffL2Norm exploded iterations converged

-- Helper functions for CSV parsing
trimStr : String -> String
trimStr str = ltrim str  -- Simplified trim

parseDoubleSimple : String -> Maybe Double
parseDoubleSimple str =
  -- Better parsing using cast
  let trimmed = trimStr str
  in case cast {to=Double} trimmed of
       d => Just d  -- Trust the cast for now

-- CSV line parsing function
parseCSVLine : String -> Maybe (Double, Double)
parseCSVLine line =
  case forget (split (== ',') (trimStr line)) of
    [xStr, yStr] => do
      x <- parseDoubleSimple (trimStr xStr)
      y <- parseDoubleSimple (trimStr yStr)
      pure (x, y)
    _ => Nothing

-- Dataset loading - properly load CSV files like the bounded version
loadUnconstrainedDataset : String -> IO (Maybe (List (Double, Double)))
loadUnconstrainedDataset filename = do
  putStrLn $ "Loading unconstrained dataset: " ++ filename
  result <- readFile filename
  case result of
    Right content => do
      let dataPoints = parseCSVContent content
      putStrLn $ "Loaded " ++ show (length dataPoints) ++ " data points"
      pure $ Just dataPoints
    Left err => do
      putStrLn $ "Error loading " ++ filename ++ ": " ++ show err
      pure Nothing
  where
    parseCSVContent : String -> List (Double, Double)
    parseCSVContent content =
      let allLines = lines content
          dataLines = drop 1 allLines  -- Skip header line
          validLines = filter (not . null) dataLines
      in mapMaybe parseCSVLine validLines

-- Main experiment runner
runUnconstrainedExperiment : String -> Nat -> IO ()
runUnconstrainedExperiment datasetName degree = do
  putStrLn $ "Running unconstrained experiment: " ++ datasetName ++ " (degree " ++ show degree ++ ")"
  
  -- Load datasets
  Just trainData <- loadUnconstrainedDataset ("datasets/" ++ datasetName ++ "_train.csv")
    | Nothing => putStrLn "Failed to load training data"
  
  Just testData <- loadUnconstrainedDataset ("datasets/" ++ datasetName ++ "_test.csv")
    | Nothing => putStrLn "Failed to load test data"
  
  -- Initialize and train model with timing
  let initialPoly = initUnconstrainedPoly degree
  ((trainedPoly, iterations, converged), executionTime) <- timeAction $
    pure $ trainUnconstrainedPoly initialPoly trainData MAX_ITERATIONS

  -- Estimate memory usage
  let memoryUsage = estimateMemoryUsage (length trainData + length testData)
  
  -- Compute metrics
  let metrics = computeUnconstrainedMetrics trainedPoly trainData testData iterations converged
  
  -- Display results
  putStrLn $ "Unconstrained Results for " ++ datasetName ++ ":"
  putStrLn $ "  Train RMSE: " ++ show (trainRMSE metrics)
  putStrLn $ "  Test RMSE: " ++ show (testRMSE metrics)
  putStrLn $ "  Train R²: " ++ show (trainR2 metrics)
  putStrLn $ "  Test R²: " ++ show (testR2 metrics)
  putStrLn $ "  Max |Coeff|: " ++ show (maxCoeff metrics)
  putStrLn $ "  Coeff L2 Norm: " ++ show (coeffL2Norm metrics)
  putStrLn $ "  Exploded: " ++ show (exploded metrics)
  putStrLn $ "  Iterations: " ++ show (UnconstrainedMetrics.iterations metrics)
  putStrLn $ "  Converged: " ++ show (UnconstrainedMetrics.converged metrics)
  putStrLn $ "  Execution Time: " ++ show executionTime ++ "s"
  putStrLn $ "  Memory Usage: " ++ show memoryUsage ++ "MB"
  putStrLn ""

-- Main function
main : IO ()
main = do
  putStrLn "Unconstrained Idris2 Polynomial Regression"
  putStrLn "=========================================="
  putStrLn ""
  putStrLn "WARNING: This version has NO coefficient bounds!"
  putStrLn "Coefficients can grow without limit and may explode."
  putStrLn ""
  
  -- Run experiments on all datasets
  runUnconstrainedExperiment "clean" 2
  runUnconstrainedExperiment "outliers" 3
  runUnconstrainedExperiment "pathological" 5
  
  putStrLn "Unconstrained experiments completed!"
  putStrLn ""
  putStrLn "Compare these results with the bounded version to see"
  putStrLn "the performance cost of safety guarantees."
