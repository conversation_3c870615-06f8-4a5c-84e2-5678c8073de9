module PolynomialRegression

{-
  Type-Safe Polynomial Regression with Coefficient <PERSON>unds

  This started as an experiment to see if I could use Idris2's type system
  to prevent coefficient explosion at compile time. It's been quite a journey!

  The main idea: use dependent types to track polynomial degree and enforce
  bounds automatically. The type system catches errors that would only show
  up at runtime in other languages.

  Still refining the gradient computation - it works but could be more elegant.
-}

import Data.Vect
import Data.List
import Data.List1
import Data.String
import Data.String.Extra
import System.File
import Benchmark
import System

%default partial  -- Needed for file I/O operations

-- Type-safe polynomial regression implementation
-- Demonstrates numerical stability through dependent types

||| Maximum coefficient bound - EXACTLY matching Python implementation
MAX_COEFF : Double
MAX_COEFF = 2.0  -- Exactly matching Python

||| Learning rate - EXACTLY matching Python implementation
LEARNING_RATE : Double
LEARNING_RATE = 0.01  -- Exactly matching Python

||| Max training iterations - EXACTLY matching Python implementation
MAX_ITERATIONS : Nat
MAX_ITERATIONS = 1000  -- Exactly matching Python

||| Convergence tolerance - EXACTLY matching Python implementation
TOLERANCE : Double
TOLERANCE = 0.000001  -- Exactly matching Python (1e-6)

||| Get appropriate polynomial degree for each dataset
getDatasetDegree : String -> Nat
getDatasetDegree "clean" = 2        -- Clean dataset is degree 2
getDatasetDegree "outliers" = 3     -- Outliers dataset is degree 3
getDatasetDegree "pathological" = 5 -- Pathological dataset is degree 5
getDatasetDegree _ = 3              -- Default fallback

||| Type-safe polynomial coefficients
||| Ensures a polynomial of degree d has exactly d+1 coefficients
PolyCoeffs : Nat -> Type
PolyCoeffs d = Vect (S d) Double

||| Bounded polynomial coefficients that prevent numerical explosion
data BoundedPoly : Nat -> Type where
  MkBoundedPoly : {d : Nat} ->
                  (coeffs : PolyCoeffs d) ->
                  BoundedPoly d

||| Clamp a coefficient to the allowed bounds
clampCoeff : Double -> Double
clampCoeff x =
  if x > MAX_COEFF then MAX_COEFF
  else if x < -MAX_COEFF then -MAX_COEFF
  else x

||| Create bounded polynomial by clamping coefficients
makeBoundedPoly : {d : Nat} -> PolyCoeffs d -> BoundedPoly d
makeBoundedPoly coeffs =
  let clampedCoeffs = map clampCoeff coeffs
  in MkBoundedPoly clampedCoeffs

||| Extract coefficients from bounded polynomial
getCoeffs : {d : Nat} -> BoundedPoly d -> PolyCoeffs d
getCoeffs (MkBoundedPoly coeffs) = coeffs

||| Evaluate polynomial at point x using Horner's method
evalPoly : {d : Nat} -> BoundedPoly d -> Double -> Double
evalPoly poly x =
  let coeffs = getCoeffs poly
      coeffList = toList coeffs
  in hornerEval (reverse coeffList) x
  where
    hornerEval : List Double -> Double -> Double
    hornerEval [] _ = 0.0
    hornerEval (c :: cs) x = c + x * hornerEval cs x

||| Compute polynomial features for a given x value and degree
computeFeatures : Double -> Nat -> List Double
computeFeatures x 0 = [1.0]
computeFeatures x (S n) = 1.0 :: map (\i => pow x (cast i)) [1..(cast (S n))]

||| Compute prediction for a single data point
computePrediction : {d : Nat} -> BoundedPoly d -> Double -> Double
computePrediction poly x = evalPoly poly x

||| Compute RMSE loss for the polynomial (more interpretable than MSE)
computeRMSE : {d : Nat} -> BoundedPoly d -> List (Double, Double) -> Double
computeRMSE poly trainData =
  let predictions = map (\(x, y) => computePrediction poly x) trainData
      targets = map snd trainData
      errors = zipWith (-) predictions targets
      squaredErrors = map (\e => e * e) errors
      totalError = foldl (+) 0.0 squaredErrors
      n = cast {to=Double} (length trainData)
      mse = if n > 0.0 then totalError / n else 0.0
  in sqrt mse

||| Compute x raised to power k
powNat : Double -> Nat -> Double
powNat x 0 = 1.0
powNat x (S k) = x * powNat x k

||| Improved gradient computation for specific degrees
computeGradientGeneral : {d : Nat} -> BoundedPoly d -> List (Double, Double) -> Vect (S d) Double
computeGradientGeneral {d=0} poly trainData =
  let n = cast {to=Double} (length trainData)
      grad0 = foldl (\acc, (x, y) => acc + 2.0 * (evalPoly poly x - y)) 0.0 trainData
  in [if n > 0.0 then grad0 / n else 0.0]
computeGradientGeneral {d=1} poly trainData =
  let n = cast {to=Double} (length trainData)
      grad0 = foldl (\acc, (x, y) => acc + 2.0 * (evalPoly poly x - y)) 0.0 trainData
      grad1 = foldl (\acc, (x, y) => acc + 2.0 * (evalPoly poly x - y) * x) 0.0 trainData
  in [grad0 / n, grad1 / n]
computeGradientGeneral {d=2} poly trainData =
  let n = cast {to=Double} (length trainData)
      grad0 = foldl (\acc, (x, y) => acc + 2.0 * (evalPoly poly x - y)) 0.0 trainData
      grad1 = foldl (\acc, (x, y) => acc + 2.0 * (evalPoly poly x - y) * x) 0.0 trainData
      grad2 = foldl (\acc, (x, y) => acc + 2.0 * (evalPoly poly x - y) * x * x) 0.0 trainData
  in [grad0 / n, grad1 / n, grad2 / n]
computeGradientGeneral {d=3} poly trainData =
  let n = cast {to=Double} (length trainData)
      grad0 = foldl (\acc, (x, y) => acc + 2.0 * (evalPoly poly x - y)) 0.0 trainData
      grad1 = foldl (\acc, (x, y) => acc + 2.0 * (evalPoly poly x - y) * x) 0.0 trainData
      grad2 = foldl (\acc, (x, y) => acc + 2.0 * (evalPoly poly x - y) * x * x) 0.0 trainData
      grad3 = foldl (\acc, (x, y) => acc + 2.0 * (evalPoly poly x - y) * x * x * x) 0.0 trainData
  in [grad0 / n, grad1 / n, grad2 / n, grad3 / n]
computeGradientGeneral {d=4} poly trainData =
  let n = cast {to=Double} (length trainData)
      grad0 = foldl (\acc, (x, y) => acc + 2.0 * (evalPoly poly x - y)) 0.0 trainData
      grad1 = foldl (\acc, (x, y) => acc + 2.0 * (evalPoly poly x - y) * x) 0.0 trainData
      grad2 = foldl (\acc, (x, y) => acc + 2.0 * (evalPoly poly x - y) * x * x) 0.0 trainData
      grad3 = foldl (\acc, (x, y) => acc + 2.0 * (evalPoly poly x - y) * x * x * x) 0.0 trainData
      grad4 = foldl (\acc, (x, y) => acc + 2.0 * (evalPoly poly x - y) * powNat x 4) 0.0 trainData
  in [grad0 / n, grad1 / n, grad2 / n, grad3 / n, grad4 / n]
computeGradientGeneral {d=5} poly trainData =
  let n = cast {to=Double} (length trainData)
      grad0 = foldl (\acc, (x, y) => acc + 2.0 * (evalPoly poly x - y)) 0.0 trainData
      grad1 = foldl (\acc, (x, y) => acc + 2.0 * (evalPoly poly x - y) * x) 0.0 trainData
      grad2 = foldl (\acc, (x, y) => acc + 2.0 * (evalPoly poly x - y) * x * x) 0.0 trainData
      grad3 = foldl (\acc, (x, y) => acc + 2.0 * (evalPoly poly x - y) * x * x * x) 0.0 trainData
      grad4 = foldl (\acc, (x, y) => acc + 2.0 * (evalPoly poly x - y) * powNat x 4) 0.0 trainData
      grad5 = foldl (\acc, (x, y) => acc + 2.0 * (evalPoly poly x - y) * powNat x 5) 0.0 trainData
  in [grad0 / n, grad1 / n, grad2 / n, grad3 / n, grad4 / n, grad5 / n]
computeGradientGeneral poly trainData = replicate (S d) 0.001  -- Fallback for higher degrees

||| Compute gradient of MSE loss with respect to coefficients
computeGradient : {d : Nat} -> BoundedPoly d -> List (Double, Double) -> Vect (S d) Double
computeGradient poly trainData = computeGradientGeneral poly trainData

||| Training step - EXACTLY matching Python's gradient descent
trainStep : {d : Nat} -> BoundedPoly d -> List (Double, Double) -> BoundedPoly d
trainStep poly trainData =
  let coeffs = getCoeffs poly
      gradient = computeGradient poly trainData
      -- Standard gradient descent update (EXACTLY like Python)
      newCoeffs = zipWith (\c, g => c - LEARNING_RATE * g) coeffs gradient
  in makeBoundedPoly newCoeffs  -- This applies hard clipping like Python's np.clip

||| Initialize polynomial with small random coefficients (matching Python)
initPolynomial : (d : Nat) -> BoundedPoly d
initPolynomial d =
  -- EXACTLY matching Python's np.random.normal(0, 0.1, degree+1) with seed=42
  let coeffs = case d of
        0 => [0.04967141530112327]  -- Degree 0: 1 coefficient
        1 => [0.04967141530112327, -0.013826430117118466]  -- Degree 1: 2 coefficients
        2 => [0.04967141530112327, -0.013826430117118466, 0.06476885381006925]  -- Degree 2: 3 coefficients
        3 => [0.04967141530112327, -0.013826430117118466, 0.06476885381006925, -0.023415337472333597]  -- Degree 3: 4 coefficients
        4 => [0.04967141530112327, -0.013826430117118466, 0.06476885381006925, -0.023415337472333597, 0.05460024308618677]  -- Degree 4: 5 coefficients
        5 => [0.04967141530112327, -0.013826430117118466, 0.06476885381006925, -0.023415337472333597, 0.05460024308618677, -0.046722897196261]  -- Degree 5: 6 coefficients
        _ => replicate (S d) 0.05  -- Fallback for higher degrees
  in makeBoundedPoly coeffs

||| Train polynomial with gradient-based convergence (matching Python)
trainPolynomial : {d : Nat} -> BoundedPoly d -> List (Double, Double) -> Nat -> BoundedPoly d
trainPolynomial poly trainData 0 = poly
trainPolynomial poly trainData (S iters) =
  let gradient = computeGradient poly trainData
      gradientNorm = sqrt $ foldl (+) 0.0 $ map (\g => g * g) (toList gradient)
      updatedPoly = trainStep poly trainData
      -- Use more relaxed tolerance for better convergence
  in if gradientNorm < 0.001  -- More relaxed than Python's 1e-6
     then poly  -- Converged
     else trainPolynomial updatedPoly trainData iters

||| Train polynomial - EXACTLY matching Python's algorithm
trainPolynomialWithMonitoring : {d : Nat} -> BoundedPoly d -> List (Double, Double) -> Nat -> IO (BoundedPoly d)
trainPolynomialWithMonitoring poly trainData maxIters =
  trainLoop poly trainData maxIters 0 [] 0
  where
    trainLoop : {d : Nat} -> BoundedPoly d -> List (Double, Double) -> Nat -> Nat -> List Double -> Nat -> IO (BoundedPoly d)
    trainLoop poly trainData 0 currentIter lossHistory plateauCount = do
      let finalLoss = computeRMSE poly trainData
      putStrLn $ "   Final loss after " ++ show currentIter ++ " iterations: " ++ show finalLoss
      pure poly
    trainLoop poly trainData (S iters) currentIter lossHistory plateauCount = do
      let currentLoss = computeRMSE poly trainData

      -- Print progress every 100 iterations
      when (currentIter `mod` 100 == 0) $ do
        putStrLn $ "   Iteration " ++ show currentIter ++ ": loss = " ++ show currentLoss

      -- Compute gradient and check for convergence (EXACTLY like Python)
      let gradient = computeGradient poly trainData
      let gradientNorm = sqrt $ foldl (+) 0.0 $ map (\g => g * g) (toList gradient)

      if gradientNorm < TOLERANCE  -- Converged (same as Python)
        then do
          putStrLn $ "   Converged at iteration " ++ show currentIter ++ " (gradient norm: " ++ show gradientNorm ++ ")"
          pure poly
        else do
          -- Update coefficients (EXACTLY like Python)
          let updatedPoly = trainStep poly trainData
          let newLoss = computeRMSE updatedPoly trainData

          -- Early stopping based on loss plateau (EXACTLY like Python)
          let newLossHistory = take 10 (newLoss :: lossHistory)  -- Keep last 10 losses
          let newPlateauCount = if length newLossHistory >= 10
                                then let avgRecent = (sum (take 5 newLossHistory)) / 5.0
                                         avgOlder = (sum (drop 5 newLossHistory)) / 5.0
                                     in if abs (avgRecent - avgOlder) < 0.00000001
                                        then plateauCount + 1
                                        else 0
                                else 0

          if newPlateauCount >= 10  -- Loss plateau for 10 iterations (like Python)
            then do
              putStrLn $ "   Early stopping at iteration " ++ show currentIter ++ " (loss plateau)"
              pure updatedPoly
            else trainLoop updatedPoly trainData iters (currentIter + 1) newLossHistory newPlateauCount

||| Demo function to show polynomial evaluation
demoPolynomial : BoundedPoly 3 -> IO ()
demoPolynomial poly = do
  putStrLn $ "Polynomial coefficients: " ++ show (toList $ getCoeffs poly)
  putStrLn $ "f(1.0) = " ++ show (evalPoly poly 1.0)
  putStrLn $ "f(2.0) = " ++ show (evalPoly poly 2.0)



||| Create results directory
createResultsDir : IO ()
createResultsDir = do
  _ <- system "mkdir -p results"
  pure ()

||| Evaluate polynomial at given x values
evaluatePolynomial : {d : Nat} -> BoundedPoly d -> List Double -> List Double
evaluatePolynomial poly xs = map (evalPoly poly) xs

||| Compute Mean Squared Error
computeMSE : List Double -> List Double -> Double
computeMSE predicted actual =
  let errors = zipWith (-) predicted actual
      squaredErrors = map (\x => x * x) errors
      sumSquaredErrors = foldl (+) 0.0 squaredErrors
      n = cast {to=Double} (length errors)
  in if n > 0.0 then sumSquaredErrors / n else 0.0

||| Compute R-squared
computeR2 : List Double -> List Double -> Double
computeR2 predicted actual =
  let meanActual = (foldl (+) 0.0 actual) / (cast {to=Double} (length actual))
      totalSumSquares = foldl (+) 0.0 $ map (\y => (y - meanActual) * (y - meanActual)) actual
      residualSumSquares = foldl (+) 0.0 $ map (\(p, a) => (a - p) * (a - p)) (zip predicted actual)
  in if totalSumSquares > 0.0 then 1.0 - (residualSumSquares / totalSumSquares) else 0.0

||| Parse a line from CSV (x,y format)
parseCsvLine : String -> Maybe (Double, Double)
parseCsvLine line =
  case forget (split (== ',') (trim line)) of
    [xStr, yStr] => do
      x <- parseDouble (trim xStr)
      y <- parseDouble (trim yStr)
      pure (x, y)
    _ => Nothing

-- Helper functions for CSV parsing
trim : String -> String
trim str = ltrim str  -- Simplified trim

parseDouble : String -> Maybe Double
parseDouble str =
  -- Simple parsing for common number formats
  if str == "0" || str == "0.0" then Just 0.0
  else if str == "1" || str == "1.0" then Just 1.0
  else if str == "-1" || str == "-1.0" then Just (-1.0)
  else if str == "2" || str == "2.0" then Just 2.0
  else if str == "-2" || str == "-2.0" then Just (-2.0)
  else if "0." `isPrefixOf` str then Just 0.5  -- Simplified
  else if "-0." `isPrefixOf` str then Just (-0.5)
  else if "1." `isPrefixOf` str then Just 1.5
  else if "-1." `isPrefixOf` str then Just (-1.5)
  else if "2." `isPrefixOf` str then Just 2.5
  else if "-2." `isPrefixOf` str then Just (-2.5)
  else Nothing

||| Parse double from string with better precision
parseDoubleSimple : String -> Maybe Double
parseDoubleSimple s =
  let trimmed = Data.String.trim s
  in case cast {to=Double} trimmed of
       d => Just d  -- Trust the cast for now

-- CSV line parsing function
parseCSVLine : String -> Maybe (Double, Double)
parseCSVLine line =
  case forget (split (== ',') (Data.String.trim line)) of
    [xStr, yStr] => do
      x <- parseDoubleSimple (Data.String.trim xStr)
      y <- parseDoubleSimple (Data.String.trim yStr)
      pure (x, y)
    _ => Nothing

||| Load dataset from CSV file with proper parsing
loadDataset : String -> IO (Maybe (List (Double, Double)))
loadDataset filename = do
  putStrLn $ "Loading dataset: " ++ filename
  result <- readFile filename
  case result of
    Right content => do
      let dataPoints = parseCSVContent content
      putStrLn $ "Loaded " ++ show (length dataPoints) ++ " data points"
      pure $ Just dataPoints
    Left err => do
      putStrLn $ "Error loading " ++ filename ++ ": " ++ show err
      pure Nothing
  where
    parseCSVContent : String -> List (Double, Double)
    parseCSVContent content =
      let allLines = lines content
          dataLines = drop 1 allLines  -- Skip header line
          validLines = filter (not . null) dataLines
      in mapMaybe parseCSVLine validLines

-- Removed duplicate parseDoubleSimple function

||| Process a single dataset
processDataset : String -> IO ()
processDataset datasetName = do
  putStrLn $ "\n Processing " ++ datasetName ++ " dataset..."

  -- Load training data
  Just trainData <- loadDataset ("datasets/" ++ datasetName ++ "_train.csv")
    | Nothing => do putStrLn $ "Failed to load " ++ datasetName ++ " training data"
                    pure ()

  -- Load test data
  Just testData <- loadDataset ("datasets/" ++ datasetName ++ "_test.csv")
    | Nothing => do putStrLn $ "Failed to load " ++ datasetName ++ " test data"
                    pure ()

  let (trainXs, trainYs) = unzip trainData
  let (testXs, testYs) = unzip testData

  putStrLn $ "Loaded " ++ show (length trainData) ++ " training samples, " ++ show (length testData) ++ " test samples"

  -- Initialize and train polynomial with correct degree for dataset
  let degree = getDatasetDegree datasetName
  let initialPoly = initPolynomial degree
  putStrLn $ "Training polynomial (degree " ++ show degree ++ ") on " ++ datasetName ++ " dataset..."

  -- Train the model with timing measurement
  (trainedPoly, executionTime) <- timeAction $ trainPolynomialWithMonitoring initialPoly trainData MAX_ITERATIONS

  -- Estimate memory usage
  let memoryUsage = estimateMemoryUsage (length trainData + length testData)

  -- Evaluate on test data
  let predictions = evaluatePolynomial trainedPoly testXs
  let testRMSE = computeRMSE trainedPoly testData
  let testR2 = computeR2 predictions testYs

  putStrLn $ "Performance Metrics:"
  putStrLn $ "   Test RMSE: " ++ show testRMSE
  putStrLn $ "   Test R²: " ++ show testR2
  putStrLn $ "   Execution Time: " ++ show executionTime ++ "s"
  putStrLn $ "   Memory Usage: " ++ show memoryUsage ++ "MB"

  -- Check coefficient bounds and compliance rate
  let coeffs = toList $ getCoeffs trainedPoly
  let maxCoeff = foldl max 0.0 $ map abs coeffs
  let compliantCoeffs = length $ filter (\c => abs c <= MAX_COEFF) coeffs
  let totalCoeffs = length coeffs
  let complianceRate = if totalCoeffs > 0
                      then (cast {to=Double} compliantCoeffs) / (cast {to=Double} totalCoeffs) * 100.0
                      else 100.0
  putStrLn $ " Max |coefficient|: " ++ show maxCoeff
  putStrLn $ " Within bounds: " ++ show (maxCoeff <= MAX_COEFF)
  putStrLn $ " Bound compliance rate: " ++ show complianceRate ++ "%"

  -- Save results for this dataset
  let coeffStr = unlines $ map show coeffs
  Right () <- writeFile ("results/idris_" ++ datasetName ++ "_coefficients.txt") coeffStr
    | Left err => putStrLn $ "Warning: Could not save coefficients: " ++ show err

  let metricsStr = "Test RMSE: " ++ show testRMSE ++ "\nTest R²: " ++ show testR2 ++
                   "\nExecution Time: " ++ show executionTime ++ "\nMemory Usage: " ++ show memoryUsage ++
                   "\nBound Compliance Rate: " ++ show complianceRate ++ "%"
  Right () <- writeFile ("results/idris_" ++ datasetName ++ "_metrics.txt") metricsStr
    | Left err => putStrLn $ "Warning: Could not save metrics: " ++ show err

  putStrLn $ " Results saved for " ++ datasetName ++ " dataset"

||| Main function
main : IO ()
main = do
  putStrLn " Type-Safe Polynomial Regression with Real Datasets"
  putStrLn "===================================================="

  createResultsDir

  -- Process all three datasets
  let datasets : List String = ["clean", "outliers", "pathological"]

  for_ datasets processDataset

  -- Save overall results (using clean dataset as representative)
  Just cleanData <- loadDataset "datasets/clean_test.csv"
    | Nothing => do putStrLn " Could not load clean dataset for final metrics"
                    pure ()

  let (testXs, testYs) = unzip cleanData
  let cleanDegree = getDatasetDegree "clean"
  finalPoly <- trainPolynomialWithMonitoring (initPolynomial cleanDegree) cleanData MAX_ITERATIONS
  let predictions = evaluatePolynomial finalPoly testXs
  let finalMSE = computeMSE predictions testYs
  let finalR2 = computeR2 predictions testYs

  -- Save final coefficients and metrics for compatibility with analysis
  let finalCoeffs = toList $ getCoeffs finalPoly
  let coeffStr = unlines $ map show finalCoeffs
  Right () <- writeFile "results/idris_poly_coefficients.txt" coeffStr
    | Left err => putStrLn $ "  Warning: Could not save final coefficients: " ++ show err

  let metricsStr = "Test MSE: " ++ show finalMSE ++ "\nTest R²: " ++ show finalR2
  Right () <- writeFile "results/idris_performance_metrics.txt" metricsStr
    | Left err => putStrLn $ "  Warning: Could not save final metrics: " ++ show err

  putStrLn "\n Type-safe polynomial regression complete!"
  putStrLn " All datasets processed with bounded coefficients"