#!/usr/bin/env python3
"""
Polynomial Regression with Different Constraint Approaches

This started as a simple comparison but grew into something more comprehensive.
I've been tweaking these implementations over several iterations to get them right.

The three main approaches:
1. Manual bounds - my go-to method, simple and effective
2. PyTorch constrained - overkill for this problem but interesting to compare
3. Unconstrained - baseline to see what we're giving up for safety

TODO: Maybe add regularization variants later?
"""

import numpy as np
import pandas as pd
from sklearn.preprocessing import PolynomialFeatures
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error, r2_score
import time
import psutil
import os
from typing import Tuple, List, Dict, Any
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')  # sklearn throws too many warnings

# These values were found through trial and error
MAX_COEFF = 2.0          # Sweet spot - not too restrictive, prevents explosion
LEARNING_RATE = 0.01     # Started with 0.1, but convergence was unstable
MAX_ITERATIONS = 1000    # Usually converges in <100 iterations anyway
TOLERANCE = 1e-6         # Good enough for most practical purposes

@dataclass
class ExperimentResults:
    """Results from a single experiment."""
    model_name: str
    dataset_name: str
    degree: int
    
    # Performance metrics
    train_rmse: float
    test_rmse: float
    train_r2: float
    test_r2: float
    
    # Computational metrics
    execution_time: float
    peak_memory_mb: float
    iterations: int
    converged: bool
    
    # Model analysis
    coefficients: List[float]
    max_coefficient: float
    bounds_compliant: bool
    bound_compliance_rate: float = 1.0  # Percentage of coefficients within bounds
    gradient_norm: float = 0.0

class MemoryMonitor:
    """Monitor memory usage during execution."""
    
    def __init__(self):
        self.initial_memory = 0
        self.peak_memory = 0
        
    def __enter__(self):
        self.process = psutil.Process(os.getpid())
        self.initial_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        current_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        self.peak_memory = current_memory - self.initial_memory

class ManualBoundedRegressor:
    """
    Custom gradient descent with coefficient bounds.

    I wrote this because I wanted full control over the optimization process.
    The coefficient clipping is simple but effective - just clamp values that
    go outside [-max_coeff, max_coeff]. Works surprisingly well in practice.
    """
    
    def __init__(self, degree: int, max_coeff: float = MAX_COEFF, 
                 learning_rate: float = LEARNING_RATE, max_iterations: int = MAX_ITERATIONS):
        self.degree = degree
        self.max_coeff = max_coeff
        self.learning_rate = learning_rate  # Might need to tune this per dataset
        self.max_iterations = max_iterations
        self.coefficients = None
        self.loss_history = []  # Track convergence for debugging

        # Initialize with small random values - found this works better than zeros
        np.random.seed(42)  # For reproducible results during development
        
    def _create_features(self, X: np.ndarray) -> np.ndarray:
        """
        Create polynomial features manually.

        Could use sklearn's PolynomialFeatures, but this gives me more control
        and is actually faster for simple cases like this.
        """
        features = np.ones((len(X), self.degree + 1))  # Start with bias term
        for i in range(1, self.degree + 1):
            features[:, i] = X ** i  # x, x^2, x^3, etc.
        return features
    
    def fit(self, X: np.ndarray, y: np.ndarray) -> Tuple[int, bool, float]:
        """
        Fit the model using gradient descent with coefficient bounds.

        Returns: (iterations_used, converged, final_loss)
        """
        # Initialize coefficients with small random values
        # Tried zeros initially, but small random works better for convergence
        self.coefficients = np.random.normal(0, 0.1, self.degree + 1)

        # Create polynomial features
        features = self._create_features(X)
        n_samples = len(X)
        
        for iteration in range(self.max_iterations):
            # Forward pass
            predictions = features @ self.coefficients
            mse = np.mean((predictions - y) ** 2)
            rmse = np.sqrt(mse)
            self.loss_history.append(rmse)  # Track RMSE for better interpretability
            
            # Compute gradient - basic chain rule application
            residuals = predictions - y
            gradient = 2 * features.T @ residuals / n_samples
            grad_magnitude = np.linalg.norm(gradient)
            
            # Check convergence - stop if gradient is small enough
            if grad_magnitude < TOLERANCE:
                return iteration + 1, True, grad_magnitude
            
            # Update coefficients
            self.coefficients -= self.learning_rate * gradient
            
            # Enforce coefficient bounds - this is the key safety mechanism
            self.coefficients = np.clip(self.coefficients, -self.max_coeff, self.max_coeff)

            # Early stopping if loss plateaus - prevents unnecessary computation
            if iteration > 10 and abs(self.loss_history[-1] - self.loss_history[-10]) < TOLERANCE:
                return iteration + 1, True, grad_magnitude

        return self.max_iterations, False, grad_magnitude
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """Make predictions."""
        features = self._create_features(X)
        return features @ self.coefficients
    
    def get_coefficients(self) -> List[float]:
        """Get model coefficients."""
        return self.coefficients.tolist()

# Removed PyTorchConstrainedRegressor - not comparable to Idris gradient descent approach

class UnconstrainedRegressor:
    """Standard polynomial regression without coefficient bounds."""

    def __init__(self, degree: int):
        self.degree = degree
        self.poly_features = PolynomialFeatures(degree=degree, include_bias=True)
        self.model = LinearRegression()
        self.fitted = False
        
    def fit(self, X: np.ndarray, y: np.ndarray) -> Tuple[int, bool, float]:
        """Fit the unconstrained model."""
        # Create polynomial features
        X_poly = self.poly_features.fit_transform(X.reshape(-1, 1))
        
        # Fit model
        self.model.fit(X_poly, y)
        self.fitted = True
        
        # LinearRegression doesn't iterate, so return 1
        return 1, True, 0.0
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """Make predictions."""
        X_poly = self.poly_features.transform(X.reshape(-1, 1))
        return self.model.predict(X_poly)
    
    def get_coefficients(self) -> List[float]:
        """Get model coefficients."""
        if hasattr(self.model, 'coef_'):
            return self.model.coef_.tolist()
        return [0.0] * (self.degree + 1)

def load_dataset(dataset_name: str) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
    """Load training and test data."""
    train_df = pd.read_csv(f'datasets/{dataset_name}_train.csv')
    test_df = pd.read_csv(f'datasets/{dataset_name}_test.csv')

    return (train_df['x'].values, train_df['y'].values,
            test_df['x'].values, test_df['y'].values)

def run_experiment(model_class, model_name: str, dataset_name: str, degree: int) -> ExperimentResults:
    """Run a single experiment."""
    print(f"Running {model_name} on {dataset_name} dataset (degree {degree})")
    
    # Load data
    X_train, y_train, X_test, y_test = load_dataset(dataset_name)
    
    with MemoryMonitor() as monitor:
        start_time = time.time()
        
        # Create and fit model
        if model_name == "Python+Unconstrained":
            model = model_class(degree)
        else:
            model = model_class(degree, MAX_COEFF)
        
        iterations, converged, grad_norm = model.fit(X_train, y_train)
        
        # Make predictions
        train_pred = model.predict(X_train)
        test_pred = model.predict(X_test)
        
        execution_time = time.time() - start_time
    
    # Compute metrics - using RMSE for better interpretability
    train_rmse = np.sqrt(mean_squared_error(y_train, train_pred))
    test_rmse = np.sqrt(mean_squared_error(y_test, test_pred))
    train_r2 = r2_score(y_train, train_pred)
    test_r2 = r2_score(y_test, test_pred)
    
    # Get coefficients and analyze bound compliance
    coefficients = model.get_coefficients()
    max_coefficient = max(abs(c) for c in coefficients) if coefficients else 0.0
    bounds_compliant = all(abs(c) <= MAX_COEFF for c in coefficients) if coefficients else False

    # Calculate bound compliance rate (percentage of coefficients within bounds)
    if coefficients:
        compliant_coeffs = sum(1 for c in coefficients if abs(c) <= MAX_COEFF)
        bound_compliance_rate = compliant_coeffs / len(coefficients)
    else:
        bound_compliance_rate = 1.0
    
    return ExperimentResults(
        model_name=model_name,
        dataset_name=dataset_name,
        degree=degree,
        train_rmse=train_rmse,
        test_rmse=test_rmse,
        train_r2=train_r2,
        test_r2=test_r2,
        execution_time=execution_time,
        peak_memory_mb=monitor.peak_memory,
        iterations=iterations,
        converged=converged,
        coefficients=coefficients,
        max_coefficient=max_coefficient,
        bounds_compliant=bounds_compliant,
        bound_compliance_rate=bound_compliance_rate,
        gradient_norm=grad_norm
    )

def save_results(results: List[ExperimentResults]):
    """Save all results to files."""
    # Create results directory
    os.makedirs('results', exist_ok=True)

    # Save detailed results as JSON
    import json
    results_data = [
        {
            'model_name': r.model_name,
            'dataset_name': r.dataset_name,
            'degree': r.degree,
            'train_rmse': r.train_rmse,
            'test_rmse': r.test_rmse,
            'train_r2': r.train_r2,
            'test_r2': r.test_r2,
            'execution_time': r.execution_time,
            'peak_memory_mb': r.peak_memory_mb,
            'iterations': r.iterations,
            'converged': r.converged,
            'coefficients': r.coefficients,
            'max_coefficient': r.max_coefficient,
            'bounds_compliant': r.bounds_compliant,
            'bound_compliance_rate': r.bound_compliance_rate,
            'gradient_norm': r.gradient_norm
        }
        for r in results
    ]

    with open('results/python_results.json', 'w') as f:
        json.dump(results_data, f, indent=2)
    
    # Save summary CSV
    summary_data = []
    for r in results:
        summary_data.append({
            'Model': r.model_name,
            'Dataset': r.dataset_name,
            'Degree': r.degree,
            'Train_R2': f"{r.train_r2:.6f}",
            'Test_R2': f"{r.test_r2:.6f}",
            'Test_RMSE': f"{r.test_rmse:.6f}",
            'Execution_Time_s': f"{r.execution_time:.4f}",
            'Peak_Memory_MB': f"{r.peak_memory_mb:.2f}",
            'Max_Coeff': f"{r.max_coefficient:.6f}",
            'Bounds_Compliant': r.bounds_compliant,
            'Bound_Compliance_Rate': f"{r.bound_compliance_rate:.3f}",
            'Converged': r.converged
        })
    
    summary_df = pd.DataFrame(summary_data)
    summary_df.to_csv('results/python_summary.csv', index=False)

    print("Python results saved to:")
    print("- results/python_results.json")
    print("- results/python_summary.csv")

def main():
    """Run all Python experiments."""
    print("Python Polynomial Regression Experiments")
    print("=" * 60)
    print(f"Max coefficient bound: ±{MAX_COEFF}")
    print(f"Learning rate: {LEARNING_RATE}")
    print(f"Max iterations: {MAX_ITERATIONS}")
    print()
    
    # Define experiments - only models comparable to Idris2 gradient descent
    models = [
        (ManualBoundedRegressor, "Python+Manual Bounds"),
        (UnconstrainedRegressor, "Python+Unconstrained")
    ]
    
    datasets = [("clean", 2), ("outliers", 3), ("pathological", 5)]
    
    all_results = []
    
    # Run all experiments
    for dataset_name, degree in datasets:
        print(f"\n{dataset_name.upper()} DATASET (Degree {degree})")
        print("-" * 40)
        
        for model_class, model_name in models:
            try:
                result = run_experiment(model_class, model_name, dataset_name, degree)
                all_results.append(result)
                
                print(f"\n{model_name} Results:")
                print(f"  Train R²: {result.train_r2:.6f}")
                print(f"  Test R²: {result.test_r2:.6f}")
                print(f"  Test RMSE: {result.test_rmse:.6f}")
                print(f"  Execution Time: {result.execution_time:.4f}s")
                print(f"  Memory Usage: {result.peak_memory_mb:.2f}MB")
                print(f"  Max |Coeff|: {result.max_coefficient:.6f}")
                print(f"  Bounds Compliant: {result.bounds_compliant}")
                # Only show compliance rate for bounded models
                if "Manual Bounds" in model_name:
                    print(f"  Bound Compliance Rate: {result.bound_compliance_rate:.1%}")
                print(f"  Converged: {result.converged}")
                
            except Exception as e:
                print(f"Error running {model_name}: {e}")
    
    # Save results
    save_results(all_results)
    
    print(f"\nAll Python experiments completed! {len(all_results)} experiments conducted.")

if __name__ == "__main__":
    main()
