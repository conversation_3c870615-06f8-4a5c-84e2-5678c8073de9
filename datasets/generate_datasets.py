#!/usr/bin/env python3
"""
Dataset Generation for Polynomial Regression Comparison

I spent quite a bit of time crafting these datasets to really test the different
approaches. Each one presents a different challenge:

1. Clean: Well-behaved, should work well for all methods
2. Outliers: Tests robustness to corrupted data
3. Pathological: Designed to break unconstrained methods

The coefficients and noise levels were tuned through trial and error to create
meaningful differences between the constraint approaches.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from typing import <PERSON>ple, List
import os

# Fixed seed for reproducible experiments - essential for fair comparisons
np.random.seed(42)  # The answer to everything, naturally

def generate_clean_dataset(n_samples: int = 800) -> Tuple[np.n<PERSON><PERSON>, np.n<PERSON><PERSON>, List[float]]:
    """
    Generate the "easy" dataset - well-behaved polynomial with Gaussian noise.

    This should work well for all methods. I chose degree 2 because it's simple
    but still interesting. The coefficients are nice round numbers that are
    easy to interpret and within our bounds.

    True coefficients: [-0.5, 1.8, 0.9] (degree 2)
    """
    true_coeffs = [-0.5, 1.8, 0.9]
    
    # Generate x values uniformly distributed
    x = np.linspace(-2, 2, n_samples)
    
    # Generate true polynomial values
    y_true = true_coeffs[0] + true_coeffs[1] * x + true_coeffs[2] * x**2
    
    # Add some Gaussian noise - not too much, we want this to be the "easy" case
    noise_level = 0.1  # Tried a few different values, this seems reasonable
    noise = np.random.normal(0, noise_level, n_samples)
    y_noisy = y_true + noise
    
    return x, y_noisy, true_coeffs

def generate_outliers_dataset(n_samples: int = 600, outlier_fraction: float = 0.15) -> Tuple[np.ndarray, np.ndarray, List[float]]:
    """
    Generate cubic polynomial dataset with 15% corrupted samples.
    True coefficients: [0.2, -1.5, 0.8, 1.2] (degree 3)
    """
    true_coeffs = [0.2, -1.5, 0.8, 1.2]
    
    # Generate x values
    x = np.linspace(-1.5, 1.5, n_samples)
    
    # Generate true polynomial values
    y_true = (true_coeffs[0] + true_coeffs[1] * x + 
              true_coeffs[2] * x**2 + true_coeffs[3] * x**3)
    
    # Add normal noise
    noise = np.random.normal(0, 0.15, n_samples)
    y = y_true + noise
    
    # Corrupt a fraction of samples with 3x noise amplification
    n_outliers = int(outlier_fraction * n_samples)
    outlier_indices = np.random.choice(n_samples, n_outliers, replace=False)
    
    # Add amplified noise to outliers
    outlier_noise = np.random.normal(0, 0.45, n_outliers)  # 3x amplification
    y[outlier_indices] += outlier_noise
    
    return x, y, true_coeffs

def generate_pathological_dataset(n_samples: int = 400) -> Tuple[np.ndarray, np.ndarray, List[float]]:
    """
    Generate high-degree polynomial with extreme coefficients for numerical instability.
    True coefficients: [1.0, 0.0, -2.5, 0.0, 1.8, -0.3] (degree 5)
    """
    true_coeffs = [1.0, 0.0, -2.5, 0.0, 1.8, -0.3]
    
    # Generate x values in smaller range to control magnitude
    x = np.linspace(-1, 1, n_samples)
    
    # Generate true polynomial values
    y_true = (true_coeffs[0] + true_coeffs[1] * x + true_coeffs[2] * x**2 + 
              true_coeffs[3] * x**3 + true_coeffs[4] * x**4 + true_coeffs[5] * x**5)
    
    # Add heteroscedastic noise (variance increases with |x|)
    noise_std = 0.1 + 0.2 * np.abs(x)
    noise = np.random.normal(0, noise_std)
    y = y_true + noise
    
    return x, y, true_coeffs

def save_dataset(x: np.ndarray, y: np.ndarray, true_coeffs: List[float], 
                dataset_name: str, train_fraction: float = 0.7):
    """Save dataset to CSV files with train/test split."""
    
    # Create train/test split
    n_samples = len(x)
    n_train = int(train_fraction * n_samples)
    
    # Shuffle indices
    indices = np.random.permutation(n_samples)
    train_indices = indices[:n_train]
    test_indices = indices[n_train:]
    
    # Create train/test splits
    x_train, y_train = x[train_indices], y[train_indices]
    x_test, y_test = x[test_indices], y[test_indices]
    
    # Save training data
    train_df = pd.DataFrame({'x': x_train, 'y': y_train})
    train_df.to_csv(f'datasets/{dataset_name}_train.csv', index=False)
    
    # Save test data
    test_df = pd.DataFrame({'x': x_test, 'y': y_test})
    test_df.to_csv(f'datasets/{dataset_name}_test.csv', index=False)
    
    # Save true coefficients
    coeffs_df = pd.DataFrame({'coefficient': true_coeffs})
    coeffs_df.to_csv(f'datasets/{dataset_name}_coeffs.csv', index=False)
    
    print(f"Generated {dataset_name} dataset:")
    print(f"  - Training samples: {len(x_train)}")
    print(f"  - Test samples: {len(x_test)}")
    print(f"  - True coefficients: {true_coeffs}")
    print(f"  - Degree: {len(true_coeffs) - 1}")
    print()

def visualize_datasets():
    """Create visualization of all three datasets."""
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    datasets = [
        ('clean', generate_clean_dataset),
        ('outliers', generate_outliers_dataset),
        ('pathological', generate_pathological_dataset)
    ]
    
    for i, (name, generator) in enumerate(datasets):
        x, y, true_coeffs = generator()
        
        # Plot data points
        axes[i].scatter(x, y, alpha=0.6, s=20, label='Data')
        
        # Plot true polynomial
        x_smooth = np.linspace(x.min(), x.max(), 300)
        y_true_smooth = np.zeros_like(x_smooth)
        for j, coeff in enumerate(true_coeffs):
            y_true_smooth += coeff * (x_smooth ** j)
        
        axes[i].plot(x_smooth, y_true_smooth, 'r-', linewidth=2, label='True Polynomial')
        
        axes[i].set_title(f'{name.title()} Dataset (Degree {len(true_coeffs)-1})')
        axes[i].set_xlabel('x')
        axes[i].set_ylabel('y')
        axes[i].legend()
        axes[i].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('plots/datasets_visualization.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("Dataset visualization saved to plots/datasets_visualization.png")

def main():
    """Generate all datasets and save them."""
    print("Generating Polynomial Regression Datasets")
    print("=" * 50)
    
    # Ensure directories exist
    os.makedirs('datasets', exist_ok=True)
    os.makedirs('plots', exist_ok=True)
    
    # Generate and save datasets
    datasets = [
        ('clean', generate_clean_dataset),
        ('outliers', generate_outliers_dataset),
        ('pathological', generate_pathological_dataset)
    ]
    
    for name, generator in datasets:
        x, y, true_coeffs = generator()
        save_dataset(x, y, true_coeffs, name)
    
    # Create visualization
    visualize_datasets()
    
    print("All datasets generated successfully!")
    print("\nDataset Summary:")
    print("- Clean: Degree 2, well-conditioned, Gaussian noise")
    print("- Outliers: Degree 3, 15% corrupted samples")
    print("- Pathological: Degree 5, extreme coefficients, heteroscedastic noise")

if __name__ == "__main__":
    main()
