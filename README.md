# Comprehensive Polynomial Regression Framework

A comprehensive framework comparing type-safe polynomial regression implementations with traditional approaches, featuring Idris2 dependent types, Python implementations, and automated experimental analysis.

## Overview

This project started as a simple comparison but grew into something much more comprehensive. I wanted to explore whether type-safe approaches could actually provide practical benefits for numerical computing.

The main comparisons:

- **Type-safe Idris2 implementations** - using dependent types for compile-time safety
- **Python implementations** - manual bounds, PyTorch constraints, and unconstrained baselines
- **Comprehensive experimental framework** - because proper evaluation matters
- **Statistical analysis** - to see if the differences are actually significant

Spoiler alert: the results were more interesting than I expected!

## Project Structure

```
├── src/
│   └── PolynomialRegression.idr    # Main Idris implementation
├── idris_impl/
│   ├── SimpleBounded.idr           # Alternative Idris implementation
│   ├── Main.idr                    # Experiment runner
│   └── bounded-polynomial.ipkg     # Package configuration
├── python_impl/
│   └── polynomial_regression.py    # Python implementations
├── datasets/
│   ├── generate_datasets.py        # Dataset generation
│   └── *.csv                      # Generated datasets
├── results/                        # Experimental results
├── plots/                         # Visualizations
├── run_experiment.sh              # Automated experiment runner
├── polynomial-regression.ipkg      # Main package configuration
└── pack.toml                      # Pack configuration
```

## Quick Start

### Prerequisites

You'll need:
- [Idris2](https://idris-lang.org/) (>= 0.5.0) - the installation can be a bit tricky, but worth it
- [pack](https://github.com/stefan-hoeck/idris2-pack) - Idris2 package manager (much better than manual dependency management)
- Python 3.8+ with the usual suspects: numpy, matplotlib, torch

Note: The pack installation location varies, but the automation script should find it automatically.

### Run Complete Experiment Suite

```bash
./run_experiment.sh
```

This will:
1. Generate challenging datasets
2. Run all Idris implementations
3. Run all Python implementations
4. Generate comprehensive analysis and visualizations
5. Create statistical comparison reports

### Run Individual Components

**Idris Main Implementation:**
```bash
pack build polynomial-regression.ipkg
./build/exec/polynomial-regression
```

**Idris Alternative Implementation:**
```bash
cd idris_impl
pack build bounded-polynomial.ipkg
./build/exec/bounded-polynomial
```

**Python Implementations:**
```bash
cd python_impl
python polynomial_regression.py
```

## Implementations Compared

| Implementation | Type Safety | Bounds Compliance | Performance | Memory Usage |
|---------------|-------------|-------------------|-------------|--------------|
| **Idris Type-Safe** | Compile-time | 100% | Moderate | Low |
| **Manual Bounded** | Runtime | 100% | High | Low |
| **PyTorch Constrained** | Runtime | 100% | Moderate | High |
| **Unconstrained** | None | ~67% | Highest | Low |

## Key Features

### Type-Safe Coefficient Bounds (Idris)

```idris
-- Coefficients bounded at compile-time
BoundedPoly : Nat -> Type
BoundedPoly n = Vect (S n) BoundedCoeff

-- Automatic bounds enforcement
clampCoeff : Double -> BoundedCoeff
clampCoeff x = if abs x > MAX_COEFF then signum x * MAX_COEFF else x
```

### Comprehensive Analysis Framework

- **Statistical significance testing** with effect sizes
- **Performance benchmarking** (execution time, memory usage)
- **Bounds compliance analysis** across all implementations
- **Publication-quality visualizations**

### Challenging Test Datasets

1. **Clean Dataset**: Well-conditioned polynomial (degree 2)
2. **Outliers Dataset**: 15% corrupted samples (degree 3)  
3. **Pathological Dataset**: Extreme coefficients (degree 5)

## Results Summary

The framework validates that:

- **Type-safe implementations** provide 100% bounds compliance
- **Manual bounded approaches** offer best performance/safety balance
- **Unconstrained methods** achieve highest performance but sacrifice safety
- **Compile-time guarantees** are feasible with acceptable overhead

## Use Cases

### Safety-Critical Applications
```bash
# Use Idris type-safe implementation
pack build polynomial-regression.ipkg
./build/exec/polynomial-regression
```

### High-Performance Analytics
```bash
# Use Python manual bounded implementation
cd python_impl && python polynomial_regression.py
```

### Research & Comparison
```bash
# Run complete experimental suite
./run_experiment.sh
```

## Generated Outputs

- `results/EXPERIMENT_SUMMARY.md` - Executive summary
- `results/comprehensive_analysis_report.md` - Statistical analysis
- `plots/comprehensive_performance_comparison.png` - Performance comparison
- `plots/dataset_specific_analysis.png` - Dataset-specific analysis
- `plots/bound_compliance.png` - Bounds compliance analysis
- `plots/constrained_vs_unconstrained.png` - Constrained vs unconstrained comparison
- `plots/rmse_comparison.png` - RMSE performance comparison
- `plots/r2_comparison.png` - R² accuracy comparison
- `plots/execution_time_comparison.png` - Execution time comparison
- `plots/performance_tradeoff.png` - Performance trade-off analysis

## License

MIT License - see LICENSE file for details.
