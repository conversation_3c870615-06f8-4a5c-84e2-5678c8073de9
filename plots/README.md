# Polynomial Regression Experimental Results - Visualizations

This directory contains comprehensive visualizations of the polynomial regression experimental results, comparing type-safe Idris2 implementations with Python baselines across multiple datasets.

## Plot Index

### 1. **comprehensive_summary.png**
**4-panel comprehensive overview of all key metrics**
- **Top Left**: RMSE performance on clean dataset (filtered for clarity)
- **Top Right**: Bound compliance rates for constrained models
- **Bottom Left**: Average execution time comparison (log scale)
- **Bottom Right**: Average memory usage comparison

*This is the primary summary visualization showing all key findings.*

### 2. **constrained_vs_unconstrained.png**
**Direct comparison between constrained and unconstrained model categories**
- **Top Left**: Constrained models RMSE (Python Manual Bounds vs Idris2 Constrained)
- **Top Right**: Unconstrained models RMSE (Python vs Idris2)
- **Bottom Left**: Constrained models execution time
- **Bottom Right**: Unconstrained models execution time

*Shows clear performance differences within each category.*

### 3. **rmse_comparison.png**
**RMSE performance across all datasets and implementations**
- 3-panel comparison: Clean, Outliers, Pathological datasets
- Shows prediction accuracy in interpretable RMSE units
- Extreme outliers filtered for better visualization
- Lower values = better performance

### 4. **r2_comparison.png**
**R² accuracy comparison across all datasets and implementations**
- 3-panel comparison showing coefficient of determination
- Reference line at R² = 0 (baseline performance)
- Extreme negative values filtered for clarity
- Higher values = better performance (closer to 1.0)

### 5. **bound_compliance.png**
**Bound compliance rates for constrained models only**
- Shows percentage of coefficients within ±2.0 bounds
- Only displays Python (Manual Bounds) and Idris2 (Constrained) results
- Green dashed line indicates perfect 100% compliance
- Validates safety guarantees of constrained implementations

### 6. **execution_time_comparison.png**
**Average execution time across all implementations**
- Log scale to handle wide range of execution times
- Error bars show standard deviation across datasets
- Demonstrates performance trade-offs of different approaches

### 7. **performance_tradeoff.png**
**2-panel trade-off analysis**
- **Left**: Accuracy (RMSE) vs Speed (Execution Time)
- **Right**: Accuracy (RMSE) vs Memory Usage
- Scatter plots showing efficiency trade-offs
- Helps identify optimal implementation choices

### 8. **datasets_visualization.png**
**Original dataset characteristics visualization**
- Shows the three experimental datasets used
- Clean: Well-conditioned degree 2 polynomial
- Outliers: Degree 3 with 15% corrupted samples  
- Pathological: Degree 5 with extreme coefficients

## Key Findings from Visualizations

### Performance Hierarchy
1. Python (Unconstrained): Best overall performance
2. Python (Manual Bounds): Best safety-performance balance
3. Idris2 (Unconstrained): Good accuracy, slower execution
4. Idris2 (Constrained): Perfect safety, needs algorithm improvements

### Safety Validation
- 100% bound compliance achieved by all constrained models
- Type-safe guarantees successfully enforced at runtime
- Zero coefficient violations across all constrained experiments

### Performance Trade-offs
- Python: 10-100x faster execution, 10-30x less memory
- Idris2: Type safety overhead, but achieves comparable accuracy
- Constrained models: Perfect compliance with minimal accuracy loss

### RMSE Interpretability
- Clean dataset: ~0.10 RMSE = 10% prediction error (excellent)
- Outliers dataset: ~0.20 RMSE = 20% prediction error (good)
- Pathological dataset: ~0.25 RMSE = 25% prediction error (challenging)

## Plot Generation

All plots are generated automatically by `../generate_plots.py` which:
- Loads experimental results from `../results/` directory
- Applies consistent color schemes and styling
- Filters extreme outliers for better visualization
- Generates publication-quality figures at 300 DPI

To regenerate plots after new experiments:
```bash
python3 generate_plots.py
```

## Color Scheme

- Python (Manual Bounds): Blue (#2E86AB)
- Python (Unconstrained): Purple (#A23B72)
- Idris2 (Constrained): Orange (#F18F01)
- Idris2 (Unconstrained): Red (#C73E1D)

Consistent colors are used across all visualizations for easy identification of implementations.
