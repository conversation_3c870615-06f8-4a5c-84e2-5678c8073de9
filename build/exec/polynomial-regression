#!/bin/sh
# @generated by Idris 0.7.0, Chez backend

set -e # exit on any error

if [ "$(uname)" = Darwin ]; then
  DIR=$(zsh -c 'printf %s "$0:A:h"' "$0")
else
  DIR=$(dirname "$(readlink -f -- "$0")")
fi
export LD_LIBRARY_PATH="$DIR/polynomial-regression_app:$LD_LIBRARY_PATH"
export DYLD_LIBRARY_PATH="$DIR/polynomial-regression_app:$DYLD_LIBRARY_PATH"
export IDRIS2_INC_SRC="$DIR/polynomial-regression_app"

"$DIR/polynomial-regression_app/polynomial-regression.so" "$@"