#!/usr/local/bin/chez --program

;; @generated by Idris 0.7.0, Chez backend
(import (chezscheme))
(case (machine-type)
  [(i3fb ti3fb a6fb ta6fb) #f]
  [(i3le ti3le a6le ta6le tarm64le) (load-shared-object "libc.so.6")]
  [(i3osx ti3osx a6osx ta6osx tarm64osx) (load-shared-object "libc.dylib")]
  [(i3nt ti3nt a6nt ta6nt) (load-shared-object "msvcrt.dll")]
  [else (load-shared-object "libc.so")])

(load-shared-object "libidris2_support.dylib")

(let ()
(define (blodwen-os)
  (case (machine-type)
    [(i3le ti3le a6le ta6le tarm64le) "unix"]  ; GNU/Linux
    [(i3ob ti3ob a6ob ta6ob tarm64ob) "unix"]  ; OpenBSD
    [(i3fb ti3fb a6fb ta6fb tarm64fb) "unix"]  ; FreeBSD
    [(i3nb ti3nb a6nb ta6nb tarm64nb) "unix"]  ; NetBSD
    [(i3osx ti3osx a6osx ta6osx tarm64osx) "darwin"]
    [(i3nt ti3nt a6nt ta6nt tarm64nt) "windows"]
    [else "unknown"]))

(define blodwen-lazy
  (lambda (f)
    (let ([evaluated #f] [res void])
      (lambda ()
        (if (not evaluated)
            (begin (set! evaluated #t)
                   (set! res (f))
                   (set! f void))
            (void))
        res))))

(define (blodwen-toSignedInt x bits)
  (if (logbit? bits x)
      (logor x (ash -1 bits))
      (logand x (sub1 (ash 1 bits)))))

(define (blodwen-toUnsignedInt x bits)
  (logand x (sub1 (ash 1 bits))))

(define (blodwen-euclidDiv a b)
  (let ((q (quotient a b))
        (r (remainder a b)))
    (if (< r 0)
      (if (> b 0) (- q 1) (+ q 1))
      q)))

(define (blodwen-euclidMod a b)
  (let ((r (remainder a b)))
    (if (< r 0)
      (if (> b 0) (+ r b) (- r b))
      r)))

; flonum constants

(define (blodwen-calcFlonumUnitRoundoff)
  (let loop [(uro 1.0)]
    (if (fl= 1.0 (fl+ 1.0 uro))
      uro
      (loop (fl/ uro 2.0)))))

(define (blodwen-calcFlonumEpsilon)
  (fl* (blodwen-calcFlonumUnitRoundoff) 2.0))

(define (blodwen-flonumNaN)
  +nan.0)

(define (blodwen-flonumInf)
  +inf.0)

; Bits

(define bu+ (lambda (x y bits) (blodwen-toUnsignedInt (+ x y) bits)))
(define bu- (lambda (x y bits) (blodwen-toUnsignedInt (- x y) bits)))
(define bu* (lambda (x y bits) (blodwen-toUnsignedInt (* x y) bits)))
(define bu/ (lambda (x y bits) (blodwen-toUnsignedInt (quotient x y) bits)))

(define bs+ (lambda (x y bits) (blodwen-toSignedInt (+ x y) bits)))
(define bs- (lambda (x y bits) (blodwen-toSignedInt (- x y) bits)))
(define bs* (lambda (x y bits) (blodwen-toSignedInt (* x y) bits)))
(define bs/ (lambda (x y bits) (blodwen-toSignedInt (blodwen-euclidDiv x y) bits)))

(define (integer->bits8 x) (logand x (sub1 (ash 1 8))))
(define (integer->bits16 x) (logand x (sub1 (ash 1 16))))
(define (integer->bits32 x) (logand x (sub1 (ash 1 32))))
(define (integer->bits64 x) (logand x (sub1 (ash 1 64))))

(define (bits16->bits8 x) (logand x (sub1 (ash 1 8))))
(define (bits32->bits8 x) (logand x (sub1 (ash 1 8))))
(define (bits64->bits8 x) (logand x (sub1 (ash 1 8))))
(define (bits32->bits16 x) (logand x (sub1 (ash 1 16))))
(define (bits64->bits16 x) (logand x (sub1 (ash 1 16))))
(define (bits64->bits32 x) (logand x (sub1 (ash 1 32))))

(define (blodwen-bits-shl-signed x y bits) (blodwen-toSignedInt (ash x y) bits))

(define (blodwen-bits-shl x y bits) (logand (ash x y) (sub1 (ash 1 bits))))

(define blodwen-shl (lambda (x y) (ash x y)))
(define blodwen-shr (lambda (x y) (ash x (- y))))
(define blodwen-and (lambda (x y) (logand x y)))
(define blodwen-or (lambda (x y) (logor x y)))
(define blodwen-xor (lambda (x y) (logxor x y)))

(define cast-num
  (lambda (x)
    (if (number? x) x 0)))
(define destroy-prefix
  (lambda (x)
    (cond
      ((equal? x "") "")
      ((equal? (string-ref x 0) #\#) "")
      (else x))))

(define exact-floor
  (lambda (x)
    (inexact->exact (floor x))))

(define exact-truncate
  (lambda (x)
    (inexact->exact (truncate x))))

(define exact-truncate-boundedInt
  (lambda (x y)
    (blodwen-toSignedInt (exact-truncate x) y)))

(define exact-truncate-boundedUInt
  (lambda (x y)
    (blodwen-toUnsignedInt (exact-truncate x) y)))

(define cast-char-boundedInt
  (lambda (x y)
    (blodwen-toSignedInt (char->integer x) y)))

(define cast-char-boundedUInt
  (lambda (x y)
    (blodwen-toUnsignedInt (char->integer x) y)))

(define cast-string-int
  (lambda (x)
    (exact-truncate (cast-num (string->number (destroy-prefix x))))))

(define cast-string-boundedInt
  (lambda (x y)
    (blodwen-toSignedInt (cast-string-int x) y)))

(define cast-string-boundedUInt
  (lambda (x y)
    (blodwen-toUnsignedInt (cast-string-int x) y)))

(define cast-int-char
  (lambda (x)
    (if (or
          (and (>= x 0) (<= x #xd7ff))
          (and (>= x #xe000) (<= x #x10ffff)))
        (integer->char x)
        (integer->char 0))))

(define cast-string-double
  (lambda (x)
    (exact->inexact (cast-num (string->number (destroy-prefix x))))))


(define (string-concat xs) (apply string-append xs))
(define (string-unpack s) (string->list s))
(define (string-pack xs) (list->string xs))

(define string-cons (lambda (x y) (string-append (string x) y)))
(define string-reverse (lambda (x)
  (list->string (reverse (string->list x)))))
(define (string-substr off len s)
    (let* ((l (string-length s))
          (b (max 0 off))
          (x (max 0 len))
          (end (min l (+ b x))))
          (if (> b l)
              ""
              (substring s b end))))

(define (blodwen-string-iterator-new s)
  0)

(define (blodwen-string-iterator-to-string _ s ofs f)
  (f (substring s ofs (string-length s))))

(define (blodwen-string-iterator-next s ofs)
  (if (>= ofs (string-length s))
      '() ; EOF
      (cons (string-ref s ofs) (+ ofs 1))))

(define either-left
  (lambda (x)
    (vector 0 x)))

(define either-right
  (lambda (x)
    (vector 1 x)))

(define blodwen-error-quit
  (lambda (msg)
    (display msg)
    (newline)
    (exit 1)))

(define (blodwen-get-line p)
    (if (port? p)
        (let ((str (get-line p)))
            (if (eof-object? str)
                ""
                str))
        void))

(define (blodwen-get-char p)
    (if (port? p)
        (let ((chr (get-char p)))
            (if (eof-object? chr)
                #\nul
                chr))
        void))

;; Buffers

(define (blodwen-new-buffer size)
  (make-bytevector size 0))

(define (blodwen-buffer-size buf)
  (bytevector-length buf))

(define (blodwen-buffer-setbyte buf loc val)
  (bytevector-u8-set! buf loc val))

(define (blodwen-buffer-getbyte buf loc)
  (bytevector-u8-ref buf loc))

(define (blodwen-buffer-setbits16 buf loc val)
  (bytevector-u16-set! buf loc val (native-endianness)))

(define (blodwen-buffer-getbits16 buf loc)
  (bytevector-u16-ref buf loc (native-endianness)))

(define (blodwen-buffer-setbits32 buf loc val)
  (bytevector-u32-set! buf loc val (native-endianness)))

(define (blodwen-buffer-getbits32 buf loc)
  (bytevector-u32-ref buf loc (native-endianness)))

(define (blodwen-buffer-setbits64 buf loc val)
  (bytevector-u64-set! buf loc val (native-endianness)))

(define (blodwen-buffer-getbits64 buf loc)
  (bytevector-u64-ref buf loc (native-endianness)))

(define (blodwen-buffer-setint8 buf loc val)
  (bytevector-s8-set! buf loc val))

(define (blodwen-buffer-getint8 buf loc)
  (bytevector-s8-ref buf loc))

(define (blodwen-buffer-setint16 buf loc val)
  (bytevector-s16-set! buf loc val (native-endianness)))

(define (blodwen-buffer-getint16 buf loc)
  (bytevector-s16-ref buf loc (native-endianness)))

(define (blodwen-buffer-setint32 buf loc val)
  (bytevector-s32-set! buf loc val (native-endianness)))

(define (blodwen-buffer-getint32 buf loc)
  (bytevector-s32-ref buf loc (native-endianness)))

(define (blodwen-buffer-setint buf loc val)
  (bytevector-s64-set! buf loc val (native-endianness)))

(define (blodwen-buffer-getint buf loc)
  (bytevector-s64-ref buf loc (native-endianness)))

(define (blodwen-buffer-setint64 buf loc val)
  (bytevector-s64-set! buf loc val (native-endianness)))

(define (blodwen-buffer-getint64 buf loc)
  (bytevector-s64-ref buf loc (native-endianness)))

(define (blodwen-buffer-setdouble buf loc val)
  (bytevector-ieee-double-set! buf loc val (native-endianness)))

(define (blodwen-buffer-getdouble buf loc)
  (bytevector-ieee-double-ref buf loc (native-endianness)))

(define (blodwen-stringbytelen str)
  (bytevector-length (string->utf8 str)))

(define (blodwen-buffer-setstring buf loc val)
  (let* [(strvec (string->utf8 val))
         (len (bytevector-length strvec))]
    (bytevector-copy! strvec 0 buf loc len)))

(define (blodwen-buffer-getstring buf loc len)
  (let [(newvec (make-bytevector len))]
    (bytevector-copy! buf loc newvec 0 len)
    (utf8->string newvec)))

(define (blodwen-buffer-copydata buf start len dest loc)
  (bytevector-copy! buf start dest loc len))

;; Threads

(define-record thread-handle (semaphore))

(define (blodwen-thread proc)
  (let [(sema (blodwen-make-semaphore 0))]
    (fork-thread (lambda () (proc (vector 0)) (blodwen-semaphore-post sema)))
    (make-thread-handle sema)
    ))

(define (blodwen-thread-wait handle)
  (blodwen-semaphore-wait (thread-handle-semaphore handle)))

;; Thread mailboxes

(define blodwen-thread-data
  (make-thread-parameter #f))

(define (blodwen-get-thread-data ty)
  (blodwen-thread-data))

(define (blodwen-set-thread-data ty a)
  (blodwen-thread-data a))

;; Semaphore

(define-record semaphore (box mutex condition))

(define (blodwen-make-semaphore init)
  (make-semaphore (box init) (make-mutex) (make-condition)))

(define (blodwen-semaphore-post sema)
  (with-mutex (semaphore-mutex sema)
    (let [(sema-box (semaphore-box sema))]
      (set-box! sema-box (+ (unbox sema-box) 1))
      (condition-signal (semaphore-condition sema))
    )))

(define (blodwen-semaphore-wait sema)
  (with-mutex (semaphore-mutex sema)
    (let [(sema-box (semaphore-box sema))]
      (when (= (unbox sema-box) 0)
        (condition-wait (semaphore-condition sema) (semaphore-mutex sema)))
      (set-box! sema-box (- (unbox sema-box) 1))
      )))

;; Barrier

(define-record barrier (count-box num-threads mutex cond))

(define (blodwen-make-barrier num-threads)
  (make-barrier (box 0) num-threads (make-mutex) (make-condition)))

(define (blodwen-barrier-wait barrier)
  (let [(count-box (barrier-count-box barrier))
        (num-threads (barrier-num-threads barrier))
        (mutex (barrier-mutex barrier))
        (condition (barrier-cond barrier))]
    (with-mutex mutex
    (let* [(count-old (unbox count-box))
           (count-new (+ count-old 1))]
      (set-box! count-box count-new)
      (if (= count-new num-threads)
          (condition-broadcast condition)
          (condition-wait condition mutex))
      ))))

;; Channel
; With thanks to Alain Zscheile (@zseri) for help with understanding condition
; variables, and figuring out where the problems were and how to solve them.

(define-record channel (read-mut read-cv read-box val-cv val-box))

(define (blodwen-make-channel ty)
  (make-channel
    (make-mutex)
    (make-condition)
    (box #t)
    (make-condition)
    (box '())
    ))

; block on the read status using read-cv until the value has been read
(define (channel-put-while-helper chan)
  (let ([read-mut (channel-read-mut chan)]
        [read-box (channel-read-box chan)]
        [read-cv  (channel-read-cv  chan)]
        )
    (if (unbox read-box)
      (void)    ; val has been read, so everything is fine
      (begin    ; otherwise, block/spin with cv
        (condition-wait read-cv read-mut)
        (channel-put-while-helper chan)
        )
      )))

(define (blodwen-channel-put ty chan val)
  (with-mutex (channel-read-mut chan)
    (channel-put-while-helper chan)
    (let ([read-box (channel-read-box chan)]
          [val-box  (channel-val-box  chan)]
          )
      (set-box! val-box val)
      (set-box! read-box #f)
      ))
  (condition-signal (channel-val-cv chan))
  )

; block on the value until it has been set
(define (channel-get-while-helper chan)
  (let ([read-mut (channel-read-mut chan)]
        [read-box (channel-read-box chan)]
        [val-cv   (channel-val-cv   chan)]
        )
    (if (unbox read-box)
      (begin
        (condition-wait val-cv read-mut)
        (channel-get-while-helper chan)
        )
      (void)
      )))

(define (blodwen-channel-get ty chan)
  (mutex-acquire (channel-read-mut chan))
  (channel-get-while-helper chan)
  (let* ([val-box  (channel-val-box  chan)]
         [read-box (channel-read-box chan)]
         [read-cv  (channel-read-cv  chan)]
         [the-val  (unbox val-box)]
         )
    (set-box! val-box '())
    (set-box! read-box #t)
    (mutex-release (channel-read-mut chan))
    (condition-signal read-cv)
    the-val))

;; Mutex

(define (blodwen-make-mutex)
  (make-mutex))
(define (blodwen-mutex-acquire mutex)
  (mutex-acquire mutex))
(define (blodwen-mutex-release mutex)
  (mutex-release mutex))

;; Condition variable

(define (blodwen-make-condition)
  (make-condition))
(define (blodwen-condition-wait condition mutex)
  (condition-wait condition mutex))
(define (blodwen-condition-wait-timeout condition mutex timeout)
  (let* [(sec (div timeout 1000000))
         (micro (mod timeout 1000000))]
    (condition-wait condition mutex (make-time 'time-duration (* 1000 micro) sec))))
(define (blodwen-condition-signal condition)
  (condition-signal condition))
(define (blodwen-condition-broadcast condition)
  (condition-broadcast condition))

;; Future

(define-record future-internal (result ready mutex signal))
(define (blodwen-make-future work)
  (let ([future (make-future-internal #f #f (make-mutex) (make-condition))])
    (fork-thread (lambda ()
      (let ([result (work)])
        (with-mutex (future-internal-mutex future)
          (set-future-internal-result! future result)
          (set-future-internal-ready! future #t)
          (condition-broadcast (future-internal-signal future))))))
    future))
(define (blodwen-await-future ty future)
  (let ([mutex (future-internal-mutex future)])
    (with-mutex mutex
      (if (not (future-internal-ready future))
          (condition-wait (future-internal-signal future) mutex))
      (future-internal-result future))))

(define (blodwen-sleep s) (sleep (make-time 'time-duration 0 s)))
(define (blodwen-usleep s)
  (let ((sec (div s 1000000))
        (micro (mod s 1000000)))
       (sleep (make-time 'time-duration (* 1000 micro) sec))))

(define (blodwen-clock-time-utc) (current-time 'time-utc))
(define (blodwen-clock-time-monotonic) (current-time 'time-monotonic))
(define (blodwen-clock-time-duration) (current-time 'time-duration))
(define (blodwen-clock-time-process) (current-time 'time-process))
(define (blodwen-clock-time-thread) (current-time 'time-thread))
(define (blodwen-clock-time-gccpu) (current-time 'time-collector-cpu))
(define (blodwen-clock-time-gcreal) (current-time 'time-collector-real))
(define (blodwen-is-time? clk) (if (time? clk) 1 0))
(define (blodwen-clock-second time) (time-second time))
(define (blodwen-clock-nanosecond time) (time-nanosecond time))


(define (blodwen-arg-count)
  (length (command-line)))

(define (blodwen-arg n)
  (if (< n (length (command-line))) (list-ref (command-line) n) ""))

(define (blodwen-hasenv var)
  (if (eq? (getenv var) #f) 0 1))

;; Randoms
(define random-seed-register 0)
(define (initialize-random-seed-once)
  (if (= (virtual-register random-seed-register) 0)
      (let ([seed (time-nanosecond (current-time))])
        (set-virtual-register! random-seed-register seed)
        (random-seed seed))))

(define (blodwen-random-seed seed)
  (set-virtual-register! random-seed-register seed)
  (random-seed seed))
(define blodwen-random
  (case-lambda
    ;; no argument, pick a real value from [0, 1.0)
    [() (begin
          (initialize-random-seed-once)
          (random 1.0))]
    ;; single argument k, pick an integral value from [0, k)
    [(k)
      (begin
        (initialize-random-seed-once)
        (if (> k 0)
              (random k)
              (assertion-violationf 'blodwen-random "invalid range argument ~a" k)))]))

;; For finalisers

(define blodwen-finaliser (make-guardian))
(define (blodwen-register-object obj proc)
  (let [(x (cons obj proc))]
       (blodwen-finaliser x)
       x))
(define blodwen-run-finalisers
  (lambda ()
    (let run ()
      (let ([x (blodwen-finaliser)])
        (when x
          (((cdr x) (car x)) 'erased)
          (run))))))

;; For creating and reading back scheme objects

; read a scheme string and evaluate it, returning 'Just result' on success
; TODO: catch exception!
(define (blodwen-eval-scheme str)
  (guard
     (x [#t '()]) ; Nothing on failure
     (box (eval (read (open-input-string str)))))
  ); box == Just

(define (blodwen-eval-okay obj)
  (if (null? obj)
      0
      1))

(define (blodwen-get-eval-result obj)
  (unbox obj))

(define (blodwen-debug-scheme obj)
  (display obj) (newline))

(define (blodwen-is-number obj)
  (if (number? obj) 1 0))

(define (blodwen-is-integer obj)
  (if (and (number? obj) (exact? obj)) 1 0))

(define (blodwen-is-float obj)
  (if (flonum? obj) 1 0))

(define (blodwen-is-char obj)
  (if (char? obj) 1 0))

(define (blodwen-is-string obj)
  (if (string? obj) 1 0))

(define (blodwen-is-procedure obj)
  (if (procedure? obj) 1 0))

(define (blodwen-is-symbol obj)
  (if (symbol? obj) 1 0))

(define (blodwen-is-vector obj)
  (if (vector? obj) 1 0))

(define (blodwen-is-nil obj)
  (if (null? obj) 1 0))

(define (blodwen-is-pair obj)
  (if (pair? obj) 1 0))

(define (blodwen-is-box obj)
  (if (box? obj) 1 0))

(define (blodwen-make-symbol str)
  (string->symbol str))

; The below rely on checking that the objects are the right type first.

(define (blodwen-vector-ref obj i)
  (vector-ref obj i))

(define (blodwen-vector-length obj)
  (vector-length obj))

(define (blodwen-vector-list obj)
  (vector->list obj))

(define (blodwen-unbox obj)
  (unbox obj))

(define (blodwen-apply obj arg)
  (obj arg))

(define (blodwen-force obj)
  (obj))

(define (blodwen-read-symbol sym)
  (symbol->string sym))

(define (blodwen-id x) x)
(define System-prim__system (lambda (farg-0 farg-1) ((foreign-procedure "idris2_system" (string) int) farg-0)))
(define PreludeC-45Types-fastUnpack (lambda (farg-0) (string-unpack farg-0)))
(define PreludeC-45Types-fastPack (lambda (farg-0) (string-pack farg-0)))
(define PreludeC-45Types-fastConcat (lambda (farg-0) (string-concat farg-0)))
(define PreludeC-45IO-prim__putStr (lambda (farg-0 farg-1) ((foreign-procedure "idris2_putStr" (string) void) farg-0)))
(define PreludeC-45IO-prim__getString (lambda (farg-0) ((foreign-procedure "idris2_getString" (void*) string) farg-0)))
(define PrimIO-prim__nullAnyPtr (lambda (farg-0) ((foreign-procedure "idris2_isNull" (void*) int) farg-0)))
(define SystemC-45FileC-45ReadWrite-prim__writeLine (lambda (farg-0 farg-1 farg-2) ((foreign-procedure "idris2_writeLine" (void* string) int) farg-0 farg-1)))
(define SystemC-45FileC-45ReadWrite-prim__seekLine (lambda (farg-0 farg-1) ((foreign-procedure "idris2_seekLine" (void*) int) farg-0)))
(define SystemC-45FileC-45ReadWrite-prim__readLine (lambda (farg-0 farg-1) ((foreign-procedure "idris2_readLine" (void*) void*) farg-0)))
(define SystemC-45FileC-45ReadWrite-prim__eof (lambda (farg-0 farg-1) ((foreign-procedure "idris2_eof" (void*) int) farg-0)))
(define SystemC-45FFI-prim__free (lambda (farg-0 farg-1) ((foreign-procedure "idris2_free" (void*) void) farg-0)))
(define SystemC-45FileC-45Error-prim__fileErrno (lambda (farg-0) ((foreign-procedure "idris2_fileErrno" () int) )))
(define SystemC-45FileC-45Error-prim__error (lambda (farg-0 farg-1) ((foreign-procedure "idris2_fileError" (void*) int) farg-0)))
(define SystemC-45Errno-prim__strerror (lambda (farg-0 farg-1) ((foreign-procedure "idris2_strerror" (int) string) farg-0)))
(define SystemC-45FileC-45Handle-prim__open (lambda (farg-0 farg-1 farg-2) ((foreign-procedure "idris2_openFile" (string string) void*) farg-0 farg-1)))
(define SystemC-45FileC-45Handle-prim__close (lambda (farg-0 farg-1) ((foreign-procedure "idris2_closeFile" (void*) void) farg-0)))
(define SystemC-45Clock-prim__osClockValid (lambda (farg-0 farg-1) (blodwen-is-time? farg-0)))
(define SystemC-45Clock-prim__osClockSecond (lambda (farg-0 farg-1) (blodwen-clock-second farg-0)))
(define SystemC-45Clock-prim__osClockNanosecond (lambda (farg-0 farg-1) (blodwen-clock-nanosecond farg-0)))
(define SystemC-45Clock-prim__clockTimeUtc (lambda (farg-0) (blodwen-clock-time-utc )))
(define SystemC-45Clock-prim__clockTimeThread (lambda (farg-0) (blodwen-clock-time-thread )))
(define SystemC-45Clock-prim__clockTimeProcess (lambda (farg-0) (blodwen-clock-time-process )))
(define SystemC-45Clock-prim__clockTimeMonotonic (lambda (farg-0) (blodwen-clock-time-monotonic )))
(define SystemC-45Clock-prim__clockTimeGcReal (lambda (farg-0) (blodwen-clock-time-gcreal )))
(define SystemC-45Clock-prim__clockTimeGcCpu (lambda (farg-0) (blodwen-clock-time-gccpu )))
(define PreludeC-45IO-u--map_Functor_IO (lambda (arg-2 arg-3 ext-0) (let ((act-2 (arg-3 ext-0))) (arg-2 act-2))))
(define csegen-6 (vector (lambda (u--b) (lambda (u--a) (lambda (u--func) (lambda (arg-8885) (lambda (eta-0) (PreludeC-45IO-u--map_Functor_IO u--func arg-8885 eta-0)))))) (lambda (u--a) (lambda (arg-9931) (lambda (eta-0) arg-9931))) (lambda (u--b) (lambda (u--a) (lambda (arg-9937) (lambda (arg-9944) (lambda (world-4) (let ((act-5 (arg-9937 world-4))) (let ((act-3 (arg-9944 world-4))) (act-5 act-3))))))))))
(define csegen-13 (cons (vector csegen-6 (lambda (u--b) (lambda (u--a) (lambda (arg-10411) (lambda (arg-10414) (lambda (world-0) (let ((act-1 (arg-10411 world-0))) ((arg-10414 act-1) world-0))))))) (lambda (u--a) (lambda (arg-10425) (lambda (world-0) (let ((act-1 (arg-10425 world-0))) (act-1 world-0)))))) (lambda (u--a) (lambda (arg-13095) arg-13095))))
(define csegen-14 (lambda (eta-0) (lambda (eta-1) (+ eta-0 eta-1))))
(define csegen-17 (vector (lambda (arg-5904) (lambda (arg-5907) (+ arg-5904 arg-5907))) (lambda (arg-5914) (lambda (arg-5917) (* arg-5914 arg-5917))) (lambda (arg-5924) (exact->inexact arg-5924))))
(define PreludeC-45Types-u--foldl_Foldable_List (lambda (arg-2 arg-3 arg-4) (if (null? arg-4) arg-3 (let ((e-2 (car arg-4))) (let ((e-3 (cdr arg-4))) (PreludeC-45Types-u--foldl_Foldable_List arg-2 ((arg-2 arg-3) e-2) e-3))))))
(define PreludeC-45Types-u--foldMap_Foldable_List (lambda (arg-2 arg-3 ext-0) (PreludeC-45Types-u--foldl_Foldable_List (lambda (u--acc) (lambda (u--elem) (let ((e-1 (car arg-2))) ((e-1 u--acc) (arg-3 u--elem))))) (let ((e-2 (cdr arg-2))) e-2) ext-0)))
(define PreludeC-45Basics-flip (lambda (arg-3 ext-0 ext-1) ((arg-3 ext-1) ext-0)))
(define PreludeC-45Types-u--foldlM_Foldable_List (lambda (arg-3 arg-4 arg-5 ext-0) (PreludeC-45Types-u--foldl_Foldable_List (lambda (u--ma) (lambda (u--b) (let ((e-2 (vector-ref arg-3 1))) ((((e-2 'erased) 'erased) u--ma) (lambda (eta-0) (PreludeC-45Basics-flip arg-4 u--b eta-0)))))) (let ((e-1 (vector-ref arg-3 0))) (let ((e-5 (vector-ref e-1 1))) ((e-5 'erased) arg-5))) ext-0)))
(define PreludeC-45Types-u--foldr_Foldable_List (lambda (arg-2 arg-3 arg-4) (if (null? arg-4) arg-3 (let ((e-2 (car arg-4))) (let ((e-3 (cdr arg-4))) ((arg-2 e-2) (PreludeC-45Types-u--foldr_Foldable_List arg-2 arg-3 e-3)))))))
(define PreludeC-45Types-u--null_Foldable_List (lambda (arg-1) (if (null? arg-1) 1 0)))
(define csegen-32 (vector (lambda (u--acc) (lambda (u--elem) (lambda (u--func) (lambda (u--init) (lambda (u--input) (PreludeC-45Types-u--foldr_Foldable_List u--func u--init u--input)))))) (lambda (u--elem) (lambda (u--acc) (lambda (u--func) (lambda (u--init) (lambda (u--input) (PreludeC-45Types-u--foldl_Foldable_List u--func u--init u--input)))))) (lambda (u--elem) (lambda (arg-10911) (PreludeC-45Types-u--null_Foldable_List arg-10911))) (lambda (u--elem) (lambda (u--acc) (lambda (u--m) (lambda (i_con-0) (lambda (u--funcM) (lambda (u--init) (lambda (u--input) (PreludeC-45Types-u--foldlM_Foldable_List i_con-0 u--funcM u--init u--input)))))))) (lambda (u--elem) (lambda (arg-10940) arg-10940)) (lambda (u--a) (lambda (u--m) (lambda (i_con-0) (lambda (u--f) (lambda (arg-10954) (PreludeC-45Types-u--foldMap_Foldable_List i_con-0 u--f arg-10954))))))))
(define csegen-57 (lambda (eta-0) (lambda (eta-1) (- eta-0 eta-1))))
(define csegen-59 (lambda (eta-0) (lambda (eta-1) (cons eta-0 eta-1))))
(define u--prim__sub_Integer (lambda (arg-0 arg-1) (- arg-0 arg-1)))
(define PolynomialRegression-MAX_ITERATIONS 1000)
(define PreludeC-45TypesC-45List-lengthPlus (lambda (arg-1 arg-2) (if (null? arg-2) arg-1 (let ((e-3 (cdr arg-2))) (PreludeC-45TypesC-45List-lengthPlus (+ arg-1 1) e-3)))))
(define PreludeC-45TypesC-45List-lengthTR (lambda (ext-0) (PreludeC-45TypesC-45List-lengthPlus 0 ext-0)))
(define PreludeC-45TypesC-45SnocList-C-60C-62C-62 (lambda (arg-1 arg-2) (if (null? arg-1) arg-2 (let ((e-2 (car arg-1))) (let ((e-3 (cdr arg-1))) (PreludeC-45TypesC-45SnocList-C-60C-62C-62 e-2 (cons e-3 arg-2)))))))
(define PreludeC-45TypesC-45List-mapAppend (lambda (arg-2 arg-3 arg-4) (if (null? arg-4) (PreludeC-45TypesC-45SnocList-C-60C-62C-62 arg-2 '()) (let ((e-1 (car arg-4))) (let ((e-2 (cdr arg-4))) (PreludeC-45TypesC-45List-mapAppend (cons arg-2 (arg-3 e-1)) arg-3 e-2))))))
(define PreludeC-45EqOrd-u--C-62_Ord_Double (lambda (arg-0 arg-1) (let ((sc0 (or (and (> arg-0 arg-1) 1) 0))) (cond ((equal? sc0 0) 0)(else 1)))))
(define DataC-45List-u--zipWith_Zippable_List (lambda (arg-3 arg-4 arg-5) (if (null? arg-4) '() (if (null? arg-5) '() (let ((e-1 (car arg-4))) (let ((e-2 (cdr arg-4))) (let ((e-4 (car arg-5))) (let ((e-5 (cdr arg-5))) (cons ((arg-3 e-1) e-4) (DataC-45List-u--zipWith_Zippable_List arg-3 e-2 e-5))))))))))
(define PolynomialRegression-computeMSE (lambda (arg-0 arg-1) (let ((u--errors (DataC-45List-u--zipWith_Zippable_List csegen-57 arg-0 arg-1))) (let ((u--squaredErrors (PreludeC-45TypesC-45List-mapAppend '() (lambda (u--x) (* u--x u--x)) u--errors))) (let ((u--sumSquaredErrors (PreludeC-45Types-u--foldl_Foldable_List csegen-14 0.0 u--squaredErrors))) (let ((u--n (exact->inexact (PreludeC-45TypesC-45List-lengthTR u--errors)))) (let ((sc0 (PreludeC-45EqOrd-u--C-62_Ord_Double u--n 0.0))) (cond ((equal? sc0 1) (/ u--sumSquaredErrors u--n)) (else 0.0)))))))))
(define DataC-45List-u--zip_Zippable_List (lambda (ext-0 ext-1) (DataC-45List-u--zipWith_Zippable_List (lambda (__leftTupleSection-0) (lambda (__infixTupleSection-0) (cons __leftTupleSection-0 __infixTupleSection-0))) ext-0 ext-1)))
(define PolynomialRegression-computeR2 (lambda (arg-0 arg-1) (let ((u--meanActual (/ (PreludeC-45Types-u--foldl_Foldable_List csegen-14 0.0 arg-1) (exact->inexact (PreludeC-45TypesC-45List-lengthTR arg-1))))) (let ((u--totalSumSquares (PreludeC-45Types-u--foldl_Foldable_List csegen-14 0.0 (PreludeC-45TypesC-45List-mapAppend '() (lambda (u--y) (* (- u--y u--meanActual) (- u--y u--meanActual))) arg-1)))) (let ((u--residualSumSquares (PreludeC-45Types-u--foldl_Foldable_List csegen-14 0.0 (PreludeC-45TypesC-45List-mapAppend '() (lambda (lamc-0) (let ((e-2 (car lamc-0))) (let ((e-3 (cdr lamc-0))) (* (- e-3 e-2) (- e-3 e-2))))) (DataC-45List-u--zip_Zippable_List arg-0 arg-1))))) (let ((sc0 (PreludeC-45EqOrd-u--C-62_Ord_Double u--totalSumSquares 0.0))) (cond ((equal? sc0 1) (- 1.0 (/ u--residualSumSquares u--totalSumSquares))) (else 0.0))))))))
(define PolynomialRegression-getCoeffs (lambda (arg-0 arg-1) (let ((e-1 (cdr arg-1))) e-1)))
(define PreludeC-45TypesC-45List-reverseOnto (lambda (arg-1 arg-2) (if (null? arg-2) arg-1 (let ((e-2 (car arg-2))) (let ((e-3 (cdr arg-2))) (PreludeC-45TypesC-45List-reverseOnto (cons e-2 arg-1) e-3))))))
(define PreludeC-45TypesC-45List-reverse (lambda (ext-0) (PreludeC-45TypesC-45List-reverseOnto '() ext-0)))
(define DataC-45Vect-foldrImpl (lambda (arg-3 arg-4 arg-5 arg-6) (if (null? arg-6) (arg-5 arg-4) (let ((e-3 (car arg-6))) (let ((e-4 (cdr arg-6))) (DataC-45Vect-foldrImpl arg-3 arg-4 (lambda (eta-0) (arg-5 ((arg-3 e-3) eta-0))) e-4))))))
(define DataC-45Vect-u--foldr_Foldable_C-40VectC-32C-36nC-41 (lambda (arg-3 arg-4 arg-5) (DataC-45Vect-foldrImpl arg-3 arg-4 (lambda (eta-0) eta-0) arg-5)))
(define DataC-45Vect-u--toList_Foldable_C-40VectC-32C-36nC-41 (lambda (ext-0) (DataC-45Vect-u--foldr_Foldable_C-40VectC-32C-36nC-41 csegen-59 '() ext-0)))
(define PolynomialRegression-n--5595-7221-u--hornerEval (lambda (arg-0 arg-1 arg-2 arg-3 arg-4) (if (null? arg-3) 0.0 (let ((e-2 (car arg-3))) (let ((e-3 (cdr arg-3))) (+ e-2 (* arg-4 (PolynomialRegression-n--5595-7221-u--hornerEval arg-0 arg-1 arg-2 e-3 arg-4))))))))
(define PolynomialRegression-evalPoly (lambda (arg-0 arg-1 arg-2) (let ((u--coeffs (PolynomialRegression-getCoeffs arg-0 arg-1))) (let ((u--coeffList (DataC-45Vect-u--toList_Foldable_C-40VectC-32C-36nC-41 u--coeffs))) (PolynomialRegression-n--5595-7221-u--hornerEval arg-0 arg-2 arg-1 (PreludeC-45TypesC-45List-reverse u--coeffList) arg-2)))))
(define PolynomialRegression-evaluatePolynomial (lambda (arg-0 arg-1 arg-2) (PreludeC-45TypesC-45List-mapAppend '() (lambda (eta-0) (PolynomialRegression-evalPoly arg-0 arg-1 eta-0)) arg-2)))
(define DataC-45String-n--3846-9250-u--unlinesC-39 (lambda (arg-0) (if (null? arg-0) '() (let ((e-2 (car arg-0))) (let ((e-3 (cdr arg-0))) (cons e-2 (cons "\xa;" (DataC-45String-n--3846-9250-u--unlinesC-39 e-3))))))))
(define DataC-45String-fastUnlines (lambda (ext-0) (PreludeC-45Types-fastConcat (DataC-45String-n--3846-9250-u--unlinesC-39 ext-0))))
(define PolynomialRegression-getDatasetDegree (lambda (arg-0) (cond ((equal? arg-0 "clean") 2) ((equal? arg-0 "outliers") 3) ((equal? arg-0 "pathological") 5)(else 3))))
(define PolynomialRegression-MAX_COEFF 2.0)
(define PreludeC-45EqOrd-u--C-60_Ord_Double (lambda (arg-0 arg-1) (let ((sc0 (or (and (< arg-0 arg-1) 1) 0))) (cond ((equal? sc0 0) 0)(else 1)))))
(define PolynomialRegression-clampCoeff (lambda (arg-0) (let ((sc0 (PreludeC-45EqOrd-u--C-62_Ord_Double arg-0 PolynomialRegression-MAX_COEFF))) (cond ((equal? sc0 1) PolynomialRegression-MAX_COEFF) (else (let ((sc1 (PreludeC-45EqOrd-u--C-60_Ord_Double arg-0 (- PolynomialRegression-MAX_COEFF)))) (cond ((equal? sc1 1) (- PolynomialRegression-MAX_COEFF)) (else arg-0))))))))
(define DataC-45Vect-u--map_Functor_C-40VectC-32C-36nC-41 (lambda (arg-3 arg-4) (if (null? arg-4) '() (let ((e-3 (car arg-4))) (let ((e-4 (cdr arg-4))) (cons (arg-3 e-3) (DataC-45Vect-u--map_Functor_C-40VectC-32C-36nC-41 arg-3 e-4)))))))
(define PolynomialRegression-makeBoundedPoly (lambda (arg-0 arg-1) (let ((u--clampedCoeffs (DataC-45Vect-u--map_Functor_C-40VectC-32C-36nC-41 (lambda (eta-0) (PolynomialRegression-clampCoeff eta-0)) arg-1))) (cons arg-0 u--clampedCoeffs))))
(define DataC-45Vect-replicate (lambda (arg-1 arg-2) (cond ((equal? arg-1 0) '())(else (let ((e-0 (- arg-1 1))) (cons arg-2 (DataC-45Vect-replicate e-0 arg-2)))))))
(define PolynomialRegression-initPolynomial (lambda (arg-0) (let ((u--coeffs (cond ((equal? arg-0 0) (cons 0.04967141530112327 '()))(else (let ((e-0 (- arg-0 1))) (cond ((equal? e-0 0) (cons 0.04967141530112327 (cons (- 0.013826430117118467) '())))(else (let ((e-1 (- e-0 1))) (cond ((equal? e-1 0) (cons 0.04967141530112327 (cons (- 0.013826430117118467) (cons 0.06476885381006925 '()))))(else (let ((e-2 (- e-1 1))) (cond ((equal? e-2 0) (cons 0.04967141530112327 (cons (- 0.013826430117118467) (cons 0.06476885381006925 (cons (- 0.023415337472333597) '())))))(else (let ((e-3 (- e-2 1))) (cond ((equal? e-3 0) (cons 0.04967141530112327 (cons (- 0.013826430117118467) (cons 0.06476885381006925 (cons (- 0.023415337472333597) (cons 0.05460024308618677 '()))))))(else (let ((e-4 (- e-3 1))) (cond ((equal? e-4 0) (cons 0.04967141530112327 (cons (- 0.013826430117118467) (cons 0.06476885381006925 (cons (- 0.023415337472333597) (cons 0.05460024308618677 (cons (- 0.046722897196261) '())))))))(else (DataC-45Vect-replicate (+ arg-0 1) 0.05)))))))))))))))))))) (PolynomialRegression-makeBoundedPoly arg-0 u--coeffs))))
(define PolynomialRegression-TOLERANCE 1e-6)
(define PolynomialRegression-powNat (lambda (arg-0 arg-1) (cond ((equal? arg-1 0) 1.0)(else (let ((e-0 (- arg-1 1))) (* arg-0 (PolynomialRegression-powNat arg-0 e-0)))))))
(define PolynomialRegression-computeGradientGeneral (lambda (arg-0 arg-1 arg-2) (cond ((equal? arg-0 0) (let ((u--n (exact->inexact (PreludeC-45TypesC-45List-lengthTR arg-2)))) (let ((u--grad0 (PreludeC-45Types-u--foldl_Foldable_List (lambda (u--acc) (lambda (lamc-0) (let ((e-2 (car lamc-0))) (let ((e-3 (cdr lamc-0))) (+ u--acc (* 2.0 (- (PolynomialRegression-evalPoly 0 arg-1 e-2) e-3))))))) 0.0 arg-2))) (cons (let ((sc1 (PreludeC-45EqOrd-u--C-62_Ord_Double u--n 0.0))) (cond ((equal? sc1 1) (/ u--grad0 u--n)) (else 0.0))) '()))))(else (let ((e-0 (- arg-0 1))) (cond ((equal? e-0 0) (let ((u--n (exact->inexact (PreludeC-45TypesC-45List-lengthTR arg-2)))) (let ((u--grad0 (PreludeC-45Types-u--foldl_Foldable_List (lambda (u--acc) (lambda (lamc-0) (let ((e-2 (car lamc-0))) (let ((e-3 (cdr lamc-0))) (+ u--acc (* 2.0 (- (PolynomialRegression-evalPoly 1 arg-1 e-2) e-3))))))) 0.0 arg-2))) (let ((u--grad1 (PreludeC-45Types-u--foldl_Foldable_List (lambda (u--acc) (lambda (lamc-0) (let ((e-2 (car lamc-0))) (let ((e-3 (cdr lamc-0))) (+ u--acc (* (* 2.0 (- (PolynomialRegression-evalPoly 1 arg-1 e-2) e-3)) e-2)))))) 0.0 arg-2))) (cons (/ u--grad0 u--n) (cons (/ u--grad1 u--n) '()))))))(else (let ((e-1 (- e-0 1))) (cond ((equal? e-1 0) (let ((u--n (exact->inexact (PreludeC-45TypesC-45List-lengthTR arg-2)))) (let ((u--grad0 (PreludeC-45Types-u--foldl_Foldable_List (lambda (u--acc) (lambda (lamc-0) (let ((e-2 (car lamc-0))) (let ((e-3 (cdr lamc-0))) (+ u--acc (* 2.0 (- (PolynomialRegression-evalPoly 2 arg-1 e-2) e-3))))))) 0.0 arg-2))) (let ((u--grad1 (PreludeC-45Types-u--foldl_Foldable_List (lambda (u--acc) (lambda (lamc-0) (let ((e-2 (car lamc-0))) (let ((e-3 (cdr lamc-0))) (+ u--acc (* (* 2.0 (- (PolynomialRegression-evalPoly 2 arg-1 e-2) e-3)) e-2)))))) 0.0 arg-2))) (let ((u--grad2 (PreludeC-45Types-u--foldl_Foldable_List (lambda (u--acc) (lambda (lamc-0) (let ((e-2 (car lamc-0))) (let ((e-3 (cdr lamc-0))) (+ u--acc (* (* (* 2.0 (- (PolynomialRegression-evalPoly 2 arg-1 e-2) e-3)) e-2) e-2)))))) 0.0 arg-2))) (cons (/ u--grad0 u--n) (cons (/ u--grad1 u--n) (cons (/ u--grad2 u--n) '()))))))))(else (let ((e-2 (- e-1 1))) (cond ((equal? e-2 0) (let ((u--n (exact->inexact (PreludeC-45TypesC-45List-lengthTR arg-2)))) (let ((u--grad0 (PreludeC-45Types-u--foldl_Foldable_List (lambda (u--acc) (lambda (lamc-0) (let ((e-4 (car lamc-0))) (let ((e-3 (cdr lamc-0))) (+ u--acc (* 2.0 (- (PolynomialRegression-evalPoly 3 arg-1 e-4) e-3))))))) 0.0 arg-2))) (let ((u--grad1 (PreludeC-45Types-u--foldl_Foldable_List (lambda (u--acc) (lambda (lamc-0) (let ((e-4 (car lamc-0))) (let ((e-3 (cdr lamc-0))) (+ u--acc (* (* 2.0 (- (PolynomialRegression-evalPoly 3 arg-1 e-4) e-3)) e-4)))))) 0.0 arg-2))) (let ((u--grad2 (PreludeC-45Types-u--foldl_Foldable_List (lambda (u--acc) (lambda (lamc-0) (let ((e-4 (car lamc-0))) (let ((e-3 (cdr lamc-0))) (+ u--acc (* (* (* 2.0 (- (PolynomialRegression-evalPoly 3 arg-1 e-4) e-3)) e-4) e-4)))))) 0.0 arg-2))) (let ((u--grad3 (PreludeC-45Types-u--foldl_Foldable_List (lambda (u--acc) (lambda (lamc-0) (let ((e-4 (car lamc-0))) (let ((e-3 (cdr lamc-0))) (+ u--acc (* (* (* (* 2.0 (- (PolynomialRegression-evalPoly 3 arg-1 e-4) e-3)) e-4) e-4) e-4)))))) 0.0 arg-2))) (cons (/ u--grad0 u--n) (cons (/ u--grad1 u--n) (cons (/ u--grad2 u--n) (cons (/ u--grad3 u--n) '()))))))))))(else (let ((e-3 (- e-2 1))) (cond ((equal? e-3 0) (let ((u--n (exact->inexact (PreludeC-45TypesC-45List-lengthTR arg-2)))) (let ((u--grad0 (PreludeC-45Types-u--foldl_Foldable_List (lambda (u--acc) (lambda (lamc-0) (let ((e-5 (car lamc-0))) (let ((e-4 (cdr lamc-0))) (+ u--acc (* 2.0 (- (PolynomialRegression-evalPoly 4 arg-1 e-5) e-4))))))) 0.0 arg-2))) (let ((u--grad1 (PreludeC-45Types-u--foldl_Foldable_List (lambda (u--acc) (lambda (lamc-0) (let ((e-5 (car lamc-0))) (let ((e-4 (cdr lamc-0))) (+ u--acc (* (* 2.0 (- (PolynomialRegression-evalPoly 4 arg-1 e-5) e-4)) e-5)))))) 0.0 arg-2))) (let ((u--grad2 (PreludeC-45Types-u--foldl_Foldable_List (lambda (u--acc) (lambda (lamc-0) (let ((e-5 (car lamc-0))) (let ((e-4 (cdr lamc-0))) (+ u--acc (* (* (* 2.0 (- (PolynomialRegression-evalPoly 4 arg-1 e-5) e-4)) e-5) e-5)))))) 0.0 arg-2))) (let ((u--grad3 (PreludeC-45Types-u--foldl_Foldable_List (lambda (u--acc) (lambda (lamc-0) (let ((e-5 (car lamc-0))) (let ((e-4 (cdr lamc-0))) (+ u--acc (* (* (* (* 2.0 (- (PolynomialRegression-evalPoly 4 arg-1 e-5) e-4)) e-5) e-5) e-5)))))) 0.0 arg-2))) (let ((u--grad4 (PreludeC-45Types-u--foldl_Foldable_List (lambda (u--acc) (lambda (lamc-0) (let ((e-5 (car lamc-0))) (let ((e-4 (cdr lamc-0))) (+ u--acc (* (* 2.0 (- (PolynomialRegression-evalPoly 4 arg-1 e-5) e-4)) (PolynomialRegression-powNat e-5 4))))))) 0.0 arg-2))) (cons (/ u--grad0 u--n) (cons (/ u--grad1 u--n) (cons (/ u--grad2 u--n) (cons (/ u--grad3 u--n) (cons (/ u--grad4 u--n) '()))))))))))))(else (let ((e-4 (- e-3 1))) (cond ((equal? e-4 0) (let ((u--n (exact->inexact (PreludeC-45TypesC-45List-lengthTR arg-2)))) (let ((u--grad0 (PreludeC-45Types-u--foldl_Foldable_List (lambda (u--acc) (lambda (lamc-0) (let ((e-6 (car lamc-0))) (let ((e-5 (cdr lamc-0))) (+ u--acc (* 2.0 (- (PolynomialRegression-evalPoly 5 arg-1 e-6) e-5))))))) 0.0 arg-2))) (let ((u--grad1 (PreludeC-45Types-u--foldl_Foldable_List (lambda (u--acc) (lambda (lamc-0) (let ((e-6 (car lamc-0))) (let ((e-5 (cdr lamc-0))) (+ u--acc (* (* 2.0 (- (PolynomialRegression-evalPoly 5 arg-1 e-6) e-5)) e-6)))))) 0.0 arg-2))) (let ((u--grad2 (PreludeC-45Types-u--foldl_Foldable_List (lambda (u--acc) (lambda (lamc-0) (let ((e-6 (car lamc-0))) (let ((e-5 (cdr lamc-0))) (+ u--acc (* (* (* 2.0 (- (PolynomialRegression-evalPoly 5 arg-1 e-6) e-5)) e-6) e-6)))))) 0.0 arg-2))) (let ((u--grad3 (PreludeC-45Types-u--foldl_Foldable_List (lambda (u--acc) (lambda (lamc-0) (let ((e-6 (car lamc-0))) (let ((e-5 (cdr lamc-0))) (+ u--acc (* (* (* (* 2.0 (- (PolynomialRegression-evalPoly 5 arg-1 e-6) e-5)) e-6) e-6) e-6)))))) 0.0 arg-2))) (let ((u--grad4 (PreludeC-45Types-u--foldl_Foldable_List (lambda (u--acc) (lambda (lamc-0) (let ((e-6 (car lamc-0))) (let ((e-5 (cdr lamc-0))) (+ u--acc (* (* 2.0 (- (PolynomialRegression-evalPoly 5 arg-1 e-6) e-5)) (PolynomialRegression-powNat e-6 4))))))) 0.0 arg-2))) (let ((u--grad5 (PreludeC-45Types-u--foldl_Foldable_List (lambda (u--acc) (lambda (lamc-0) (let ((e-6 (car lamc-0))) (let ((e-5 (cdr lamc-0))) (+ u--acc (* (* 2.0 (- (PolynomialRegression-evalPoly 5 arg-1 e-6) e-5)) (PolynomialRegression-powNat e-6 5))))))) 0.0 arg-2))) (cons (/ u--grad0 u--n) (cons (/ u--grad1 u--n) (cons (/ u--grad2 u--n) (cons (/ u--grad3 u--n) (cons (/ u--grad4 u--n) (cons (/ u--grad5 u--n) '()))))))))))))))(else (DataC-45Vect-replicate (+ arg-0 1) 0.001))))))))))))))))))))
(define PolynomialRegression-computeGradient (lambda (arg-0 arg-1 arg-2) (PolynomialRegression-computeGradientGeneral arg-0 arg-1 arg-2)))
(define PolynomialRegression-computePrediction (lambda (arg-0 arg-1 arg-2) (PolynomialRegression-evalPoly arg-0 arg-1 arg-2)))
(define Builtin-snd (lambda (arg-2) (let ((e-3 (cdr arg-2))) e-3)))
(define PolynomialRegression-computeRMSE (lambda (arg-0 arg-1 arg-2) (let ((u--predictions (PreludeC-45TypesC-45List-mapAppend '() (lambda (lamc-0) (let ((e-2 (car lamc-0))) (PolynomialRegression-computePrediction arg-0 arg-1 e-2))) arg-2))) (let ((u--targets (PreludeC-45TypesC-45List-mapAppend '() (lambda (eta-0) (Builtin-snd eta-0)) arg-2))) (let ((u--errors (DataC-45List-u--zipWith_Zippable_List csegen-57 u--predictions u--targets))) (let ((u--squaredErrors (PreludeC-45TypesC-45List-mapAppend '() (lambda (u--e) (* u--e u--e)) u--errors))) (let ((u--totalError (PreludeC-45Types-u--foldl_Foldable_List csegen-14 0.0 u--squaredErrors))) (let ((u--n (exact->inexact (PreludeC-45TypesC-45List-lengthTR arg-2)))) (let ((u--mse (let ((sc0 (PreludeC-45EqOrd-u--C-62_Ord_Double u--n 0.0))) (cond ((equal? sc0 1) (/ u--totalError u--n)) (else 0.0))))) (flsqrt u--mse))))))))))
(define DataC-45List-drop (lambda (arg-1 arg-2) (cond ((equal? arg-1 0) arg-2)(else (let ((e-0 (- arg-1 1))) (if (null? arg-2) '() (let ((e-4 (cdr arg-2))) (DataC-45List-drop e-0 e-4))))))))
(define DataC-45Nat-lte (lambda (arg-0 arg-1) (cond ((equal? arg-0 0) 1)(else (cond ((equal? arg-1 0) 0)(else (cond ((equal? arg-0 0) (blodwen-error-quit "Nat case not covered"))(else (let ((e-0 (- arg-0 1))) (cond ((equal? arg-1 0) (blodwen-error-quit "Nat case not covered"))(else (let ((e-1 (- arg-1 1))) (DataC-45Nat-lte e-0 e-1)))))))))))))
(define PreludeC-45Types-prim__integerToNat (lambda (arg-0) (let ((sc0 (or (and (<= 0 arg-0) 1) 0))) (cond ((equal? sc0 0) 0)(else arg-0)))))
(define DataC-45Nat-modC-39 (lambda (arg-0 arg-1 arg-2) (cond ((equal? arg-0 0) arg-1)(else (let ((e-0 (- arg-0 1))) (let ((sc0 (DataC-45Nat-lte arg-1 arg-2))) (cond ((equal? sc0 1) arg-1) (else (DataC-45Nat-modC-39 e-0 (PreludeC-45Types-prim__integerToNat (- arg-1 (+ arg-2 1))) arg-2)))))))))
(define PreludeC-45Uninhabited-void (lambda (ext-0) (display "Error: Executed 'void'")))
(define DataC-45Nat-modNatNZ (lambda (arg-0 arg-1) (cond ((equal? arg-1 0) (PreludeC-45Uninhabited-void 'erased))(else (let ((e-0 (- arg-1 1))) (DataC-45Nat-modC-39 arg-0 arg-0 e-0))))))
(define DataC-45Nat-modNat (lambda (arg-0 arg-1) (cond ((equal? arg-1 0) (blodwen-error-quit (string-append "ERROR: " "Unhandled input for Data.Nat.modNat at Data.Nat:355:1--355:59")))(else (let ((e-0 (- arg-1 1))) (DataC-45Nat-modNatNZ arg-0 (+ e-0 1)))))))
(define PreludeC-45InterfacesC-45NumC-45Semigroup-u--C-60C-43C-62_Semigroup_AdditiveC-36a (lambda (arg-1 ext-0 ext-1) (let ((e-1 (vector-ref arg-1 0))) ((e-1 ext-0) ext-1))))
(define PreludeC-45InterfacesC-45NumC-45Monoid-u--neutral_Monoid_AdditiveC-36a (lambda (arg-1) (let ((e-3 (vector-ref arg-1 2))) (e-3 0))))
(define PreludeC-45Interfaces-sum (lambda (arg-2 arg-3 ext-0) (let ((e-6 (vector-ref arg-3 5))) (((((e-6 'erased) 'erased) (cons (lambda (arg-8474) (lambda (arg-8477) (PreludeC-45InterfacesC-45NumC-45Semigroup-u--C-60C-43C-62_Semigroup_AdditiveC-36a arg-2 arg-8474 arg-8477))) (PreludeC-45InterfacesC-45NumC-45Monoid-u--neutral_Monoid_AdditiveC-36a arg-2))) (lambda (eta-0) eta-0)) ext-0))))
(define DataC-45List-take (lambda (arg-1 arg-2) (cond ((equal? arg-1 0) '())(else (let ((e-0 (- arg-1 1))) (if (null? arg-2) '() (let ((e-2 (car arg-2))) (let ((e-3 (cdr arg-2))) (cons e-2 (DataC-45List-take e-0 e-3))))))))))
(define PolynomialRegression-LEARNING_RATE 0.01)
(define DataC-45Vect-u--zipWith_Zippable_C-40VectC-32C-36kC-41 (lambda (arg-4 arg-5 arg-6) (if (null? arg-5) '() (let ((e-3 (car arg-5))) (let ((e-4 (cdr arg-5))) (let ((e-8 (car arg-6))) (let ((e-9 (cdr arg-6))) (cons ((arg-4 e-3) e-8) (DataC-45Vect-u--zipWith_Zippable_C-40VectC-32C-36kC-41 arg-4 e-4 e-9)))))))))
(define PolynomialRegression-trainStep (lambda (arg-0 arg-1 arg-2) (let ((u--coeffs (PolynomialRegression-getCoeffs arg-0 arg-1))) (let ((u--gradient (PolynomialRegression-computeGradient arg-0 arg-1 arg-2))) (let ((u--newCoeffs (DataC-45Vect-u--zipWith_Zippable_C-40VectC-32C-36kC-41 (lambda (u--c) (lambda (u--g) (- u--c (* PolynomialRegression-LEARNING_RATE u--g)))) u--coeffs u--gradient))) (PolynomialRegression-makeBoundedPoly arg-0 u--newCoeffs))))))
(define PreludeC-45Interfaces-when (lambda (arg-1 arg-2 arg-3) (cond ((equal? arg-2 1) (arg-3)) (else (let ((e-2 (vector-ref arg-1 1))) ((e-2 'erased) (vector 0 )))))))
(define PreludeC-45EqOrd-u--C-61C-61_Eq_Ordering (lambda (arg-0 arg-1) (cond ((equal? arg-0 0) (cond ((equal? arg-1 0) 1)(else 0))) ((equal? arg-0 1) (cond ((equal? arg-1 1) 1)(else 0))) ((equal? arg-0 2) (cond ((equal? arg-1 2) 1)(else 0)))(else 0))))
(define PreludeC-45EqOrd-u--C-47C-61_Eq_Ordering (lambda (arg-0 arg-1) (let ((sc0 (PreludeC-45EqOrd-u--C-61C-61_Eq_Ordering arg-0 arg-1))) (cond ((equal? sc0 1) 0) (else 1)))))
(define PreludeC-45EqOrd-u--C-60_Ord_Integer (lambda (arg-0 arg-1) (let ((sc0 (or (and (< arg-0 arg-1) 1) 0))) (cond ((equal? sc0 0) 0)(else 1)))))
(define PreludeC-45EqOrd-u--C-61C-61_Eq_Integer (lambda (arg-0 arg-1) (let ((sc0 (or (and (= arg-0 arg-1) 1) 0))) (cond ((equal? sc0 0) 0)(else 1)))))
(define PreludeC-45EqOrd-u--compare_Ord_Integer (lambda (arg-0 arg-1) (let ((sc0 (PreludeC-45EqOrd-u--C-60_Ord_Integer arg-0 arg-1))) (cond ((equal? sc0 1) 0) (else (let ((sc1 (PreludeC-45EqOrd-u--C-61C-61_Eq_Integer arg-0 arg-1))) (cond ((equal? sc1 1) 1) (else 2))))))))
(define PreludeC-45Types-u--C-62C-61_Ord_Nat (lambda (arg-0 arg-1) (PreludeC-45EqOrd-u--C-47C-61_Eq_Ordering (PreludeC-45EqOrd-u--compare_Ord_Integer arg-0 arg-1) 0)))
(define PreludeC-45Num-u--abs_Abs_Double (lambda (arg-0) (let ((sc0 (PreludeC-45EqOrd-u--C-60_Ord_Double arg-0 (exact->inexact 0)))) (cond ((equal? sc0 1) (- arg-0)) (else arg-0)))))
(define PreludeC-45Show-firstCharIs (lambda (arg-0 arg-1) (cond ((equal? arg-1 "") 0)(else (arg-0 (string-ref arg-1 0))))))
(define PreludeC-45Show-showParens (lambda (arg-0 arg-1) (cond ((equal? arg-0 0) arg-1) (else (string-append "(" (string-append arg-1 ")"))))))
(define PreludeC-45EqOrd-u--C-61C-61_Eq_Char (lambda (arg-0 arg-1) (let ((sc0 (or (and (char=? arg-0 arg-1) 1) 0))) (cond ((equal? sc0 0) 0)(else 1)))))
(define PreludeC-45Show-precCon (lambda (arg-0) (case (vector-ref arg-0 0) ((0) 0) ((1) 1) ((2) 2) ((3) 3) ((4) 4) ((5) 5) (else 6))))
(define PreludeC-45Show-u--compare_Ord_Prec (lambda (arg-0 arg-1) (case (vector-ref arg-0 0) ((4) (let ((e-0 (vector-ref arg-0 1))) (case (vector-ref arg-1 0) ((4) (let ((e-1 (vector-ref arg-1 1))) (PreludeC-45EqOrd-u--compare_Ord_Integer e-0 e-1)))(else (PreludeC-45EqOrd-u--compare_Ord_Integer (PreludeC-45Show-precCon arg-0) (PreludeC-45Show-precCon arg-1))))))(else (PreludeC-45EqOrd-u--compare_Ord_Integer (PreludeC-45Show-precCon arg-0) (PreludeC-45Show-precCon arg-1))))))
(define PreludeC-45Show-u--C-62C-61_Ord_Prec (lambda (arg-0 arg-1) (PreludeC-45EqOrd-u--C-47C-61_Eq_Ordering (PreludeC-45Show-u--compare_Ord_Prec arg-0 arg-1) 0)))
(define PreludeC-45Show-primNumShow (lambda (arg-1 arg-2 arg-3) (let ((u--str (arg-1 arg-3))) (PreludeC-45Show-showParens (let ((sc0 (PreludeC-45Show-u--C-62C-61_Ord_Prec arg-2 (vector 5 )))) (cond ((equal? sc0 1) (PreludeC-45Show-firstCharIs (lambda (arg-0) (PreludeC-45EqOrd-u--C-61C-61_Eq_Char arg-0 #\-)) u--str)) (else 0))) u--str))))
(define PreludeC-45Show-u--showPrec_Show_Double (lambda (ext-0 ext-1) (PreludeC-45Show-primNumShow (lambda (eta-0) (number->string eta-0)) ext-0 ext-1)))
(define PreludeC-45Show-u--show_Show_Double (lambda (arg-0) (PreludeC-45Show-u--showPrec_Show_Double (vector 0 ) arg-0)))
(define PreludeC-45Show-u--showPrec_Show_Integer (lambda (ext-0 ext-1) (PreludeC-45Show-primNumShow (lambda (eta-0) (number->string eta-0)) ext-0 ext-1)))
(define PreludeC-45Show-u--show_Show_Integer (lambda (arg-0) (PreludeC-45Show-u--showPrec_Show_Integer (vector 0 ) arg-0)))
(define PreludeC-45Show-u--show_Show_Nat (lambda (arg-0) (PreludeC-45Show-u--show_Show_Integer arg-0)))
(define PolynomialRegression-n--7618-9054-u--trainLoop (lambda (arg-0 arg-1 arg-2 arg-3 arg-4 arg-5 arg-6 arg-7 arg-8 arg-9 arg-10) (cond ((equal? arg-7 0) (let ((u--finalLoss (PolynomialRegression-computeRMSE arg-4 arg-5 arg-6))) (lambda (world-0) (let ((act-1 (PreludeC-45IO-prim__putStr (string-append (string-append "   Final loss after " (string-append (PreludeC-45Show-u--show_Show_Nat arg-8) (string-append " iterations: " (PreludeC-45Show-u--show_Show_Double u--finalLoss)))) "\xa;") world-0))) arg-5))))(else (let ((e-0 (- arg-7 1))) (let ((u--currentLoss (PolynomialRegression-computeRMSE arg-4 arg-5 arg-6))) (lambda (world-0) (let ((act-1 ((PreludeC-45Interfaces-when csegen-6 (or (and (= (DataC-45Nat-modNat arg-8 100) 0) 1) 0) (lambda () (lambda (eta-0) (PreludeC-45IO-prim__putStr (string-append (string-append "   Iteration " (string-append (PreludeC-45Show-u--show_Show_Nat arg-8) (string-append ": loss = " (PreludeC-45Show-u--show_Show_Double u--currentLoss)))) "\xa;") eta-0)))) world-0))) (((let ((u--gradient (PolynomialRegression-computeGradient arg-4 arg-5 arg-6))) (let ((u--gradientNorm (flsqrt (PreludeC-45Types-u--foldl_Foldable_List csegen-14 0.0 (PreludeC-45TypesC-45List-mapAppend '() (lambda (u--g) (* u--g u--g)) (DataC-45Vect-u--toList_Foldable_C-40VectC-32C-36nC-41 u--gradient)))))) (let ((sc0 (PreludeC-45EqOrd-u--C-60_Ord_Double u--gradientNorm PolynomialRegression-TOLERANCE))) (cond ((equal? sc0 1) (lambda () (lambda (world-1) (let ((act-2 (PreludeC-45IO-prim__putStr (string-append (string-append "   Converged at iteration " (string-append (PreludeC-45Show-u--show_Show_Nat arg-8) (string-append " (gradient norm: " (string-append (PreludeC-45Show-u--show_Show_Double u--gradientNorm) ")")))) "\xa;") world-1))) arg-5)))) (else (let ((u--updatedPoly (PolynomialRegression-trainStep arg-4 arg-5 arg-6))) (let ((u--newLoss (PolynomialRegression-computeRMSE arg-4 u--updatedPoly arg-6))) (let ((u--newLossHistory (DataC-45List-take 10 (cons u--newLoss arg-9)))) (let ((u--newPlateauCount (let ((sc1 (PreludeC-45Types-u--C-62C-61_Ord_Nat (PreludeC-45TypesC-45List-lengthTR u--newLossHistory) 10))) (cond ((equal? sc1 1) (let ((u--avgRecent (/ (PreludeC-45Interfaces-sum csegen-17 csegen-32 (DataC-45List-take 5 u--newLossHistory)) 5.0))) (let ((u--avgOlder (/ (PreludeC-45Interfaces-sum csegen-17 csegen-32 (DataC-45List-drop 5 u--newLossHistory)) 5.0))) (let ((sc2 (PreludeC-45EqOrd-u--C-60_Ord_Double (PreludeC-45Num-u--abs_Abs_Double (- u--avgRecent u--avgOlder)) 1e-8))) (cond ((equal? sc2 1) (+ arg-10 1)) (else 0)))))) (else 0))))) (let ((sc1 (PreludeC-45Types-u--C-62C-61_Ord_Nat u--newPlateauCount 10))) (cond ((equal? sc1 1) (lambda () (lambda (world-1) (let ((act-2 (PreludeC-45IO-prim__putStr (string-append (string-append "   Early stopping at iteration " (string-append (PreludeC-45Show-u--show_Show_Nat arg-8) " (loss plateau)")) "\xa;") world-1))) u--updatedPoly)))) (else (lambda () (PolynomialRegression-n--7618-9054-u--trainLoop arg-0 arg-1 arg-2 arg-3 arg-4 u--updatedPoly arg-6 e-0 (+ arg-8 1) u--newLossHistory u--newPlateauCount))))))))))))))) world-0)))))))))
(define PolynomialRegression-trainPolynomialWithMonitoring (lambda (arg-0 arg-1 arg-2 arg-3) (PolynomialRegression-n--7618-9054-u--trainLoop arg-0 arg-3 arg-2 arg-1 arg-0 arg-1 arg-2 arg-3 0 '() 0)))
(define SystemC-45FileC-45Support-ok (lambda (arg-3 arg-4) (let ((e-1 (car arg-3))) (let ((e-5 (vector-ref e-1 0))) (let ((e-7 (vector-ref e-5 1))) ((e-7 'erased) (vector 1 arg-4)))))))
(define SystemC-45FileC-45Error-returnError (lambda (arg-2) (let ((e-1 (car arg-2))) (let ((e-4 (vector-ref e-1 1))) ((((e-4 'erased) 'erased) (let ((e-6 (cdr arg-2))) ((e-6 'erased) (lambda (eta-0) (SystemC-45FileC-45Error-prim__fileErrno eta-0))))) (lambda (u--err) (let ((e-7 (car arg-2))) (let ((e-10 (vector-ref e-7 0))) (let ((e-12 (vector-ref e-10 1))) ((e-12 'erased) (vector 0 (cond ((equal? u--err 0) (vector 1 )) ((equal? u--err 1) (vector 2 )) ((equal? u--err 2) (vector 3 )) ((equal? u--err 3) (vector 4 )) ((equal? u--err 4) (vector 5 ))(else (vector 0 (bs- u--err 5 63)))))))))))))))
(define PreludeC-45EqOrd-u--C-61C-61_Eq_Int (lambda (arg-0 arg-1) (let ((sc0 (or (and (= arg-0 arg-1) 1) 0))) (cond ((equal? sc0 0) 0)(else 1)))))
(define SystemC-45FileC-45ReadWrite-fPutStr (lambda (arg-1 arg-2 arg-3) (let ((e-1 (car arg-1))) (let ((e-4 (vector-ref e-1 1))) ((((e-4 'erased) 'erased) (let ((e-6 (cdr arg-1))) ((e-6 'erased) (lambda (eta-0) (SystemC-45FileC-45ReadWrite-prim__writeLine arg-2 arg-3 eta-0))))) (lambda (u--res) (let ((sc1 (PreludeC-45EqOrd-u--C-61C-61_Eq_Int u--res (blodwen-toSignedInt 0 63)))) (cond ((equal? sc1 1) (SystemC-45FileC-45Error-returnError arg-1)) (else (SystemC-45FileC-45Support-ok arg-1 (vector 0 )))))))))))
(define SystemC-45FileC-45Handle-closeFile (lambda (arg-1 arg-2) (let ((e-2 (cdr arg-1))) ((e-2 'erased) (lambda (eta-0) (SystemC-45FileC-45Handle-prim__close arg-2 eta-0))))))
(define PreludeC-45InterfacesC-45BoolC-45Semigroup-u--C-60C-43C-62_Semigroup_AnyBool (lambda (arg-0 arg-1) (cond ((equal? arg-0 1) 1) (else arg-1))))
(define PreludeC-45Types-elemBy (lambda (arg-2 arg-3 arg-4 ext-0) (let ((e-6 (vector-ref arg-2 5))) (((((e-6 'erased) 'erased) (cons (lambda (arg-8474) (lambda (arg-8477) (PreludeC-45InterfacesC-45BoolC-45Semigroup-u--C-60C-43C-62_Semigroup_AnyBool arg-8474 arg-8477))) 0)) (arg-3 arg-4)) ext-0))))
(define PreludeC-45Types-elem (lambda (arg-2 arg-3 ext-1 ext-0) (PreludeC-45Types-elemBy arg-2 (lambda (eta-0) (lambda (eta-1) (let ((e-1 (car arg-3))) ((e-1 eta-0) eta-1)))) ext-1 ext-0)))
(define SystemC-45Info-os (blodwen-os))
(define PreludeC-45EqOrd-u--C-61C-61_Eq_String (lambda (arg-0 arg-1) (let ((sc0 (or (and (string=? arg-0 arg-1) 1) 0))) (cond ((equal? sc0 0) 0)(else 1)))))
(define PreludeC-45EqOrd-u--C-47C-61_Eq_String (lambda (arg-0 arg-1) (let ((sc0 (PreludeC-45EqOrd-u--C-61C-61_Eq_String arg-0 arg-1))) (cond ((equal? sc0 1) 0) (else 1)))))
(define SystemC-45Info-isWindows (PreludeC-45Types-elem csegen-32 (cons (lambda (arg-676) (lambda (arg-679) (PreludeC-45EqOrd-u--C-61C-61_Eq_String arg-676 arg-679))) (lambda (arg-686) (lambda (arg-689) (PreludeC-45EqOrd-u--C-47C-61_Eq_String arg-686 arg-689)))) SystemC-45Info-os (cons "windows" (cons "mingw32" (cons "cygwin32" '())))))
(define SystemC-45FileC-45Mode-modeStr (lambda (arg-0) (cond ((equal? arg-0 0) (let ((sc1 SystemC-45Info-isWindows)) (cond ((equal? sc1 1) "rb") (else "r")))) ((equal? arg-0 1) (let ((sc1 SystemC-45Info-isWindows)) (cond ((equal? sc1 1) "wb") (else "w")))) ((equal? arg-0 2) (let ((sc1 SystemC-45Info-isWindows)) (cond ((equal? sc1 1) "ab") (else "a")))) ((equal? arg-0 3) (let ((sc1 SystemC-45Info-isWindows)) (cond ((equal? sc1 1) "rb+") (else "r+")))) ((equal? arg-0 4) (let ((sc1 SystemC-45Info-isWindows)) (cond ((equal? sc1 1) "wb+") (else "w+")))) (else (let ((sc1 SystemC-45Info-isWindows)) (cond ((equal? sc1 1) "ab+") (else "a+")))))))
(define PreludeC-45EqOrd-u--C-47C-61_Eq_Int (lambda (arg-0 arg-1) (let ((sc0 (PreludeC-45EqOrd-u--C-61C-61_Eq_Int arg-0 arg-1))) (cond ((equal? sc0 1) 0) (else 1)))))
(define SystemC-45FileC-45Handle-openFile (lambda (arg-1 arg-2 arg-3) (let ((e-1 (car arg-1))) (let ((e-4 (vector-ref e-1 1))) ((((e-4 'erased) 'erased) (let ((e-6 (cdr arg-1))) ((e-6 'erased) (lambda (eta-0) (SystemC-45FileC-45Handle-prim__open arg-2 (SystemC-45FileC-45Mode-modeStr arg-3) eta-0))))) (lambda (u--res) (let ((sc1 (PreludeC-45EqOrd-u--C-47C-61_Eq_Int (PrimIO-prim__nullAnyPtr u--res) (blodwen-toSignedInt 0 63)))) (cond ((equal? sc1 1) (SystemC-45FileC-45Error-returnError arg-1)) (else (SystemC-45FileC-45Support-ok arg-1 u--res))))))))))
(define SystemC-45FileC-45Handle-withFile (lambda (arg-3 arg-4 arg-5 arg-6 arg-7) (let ((e-1 (car arg-3))) (let ((e-4 (vector-ref e-1 1))) ((((e-4 'erased) 'erased) (SystemC-45FileC-45Handle-openFile arg-3 arg-4 arg-5)) (lambda (_-0) (case (vector-ref _-0 0) ((1) (let ((e-6 (vector-ref _-0 1))) (let ((e-8 (car arg-3))) (let ((e-10 (vector-ref e-8 1))) ((((e-10 'erased) 'erased) (arg-7 e-6)) (lambda (u--res) (let ((e-13 (car arg-3))) (let ((e-15 (vector-ref e-13 1))) ((((e-15 'erased) 'erased) (SystemC-45FileC-45Handle-closeFile arg-3 e-6)) (lambda (_-10649) (let ((e-18 (car arg-3))) (let ((e-21 (vector-ref e-18 0))) (let ((e-23 (vector-ref e-21 1))) ((e-23 'erased) u--res)))))))))))))) (else (let ((e-6 (vector-ref _-0 1))) (let ((e-8 (car arg-3))) (let ((e-11 (vector-ref e-8 0))) (let ((e-14 (vector-ref e-11 0))) ((((e-14 'erased) 'erased) (lambda (eta-0) (vector 0 eta-0))) (arg-6 e-6))))))))))))))
(define SystemC-45FileC-45ReadWrite-writeFile (lambda (arg-1 arg-2 arg-3) (SystemC-45FileC-45Handle-withFile arg-1 arg-2 1 (lambda (eta-0) (let ((e-1 (car arg-1))) (let ((e-5 (vector-ref e-1 0))) (let ((e-7 (vector-ref e-5 1))) ((e-7 'erased) eta-0))))) (lambda (eta-0) (PreludeC-45Basics-flip (lambda (eta-1) (lambda (eta-2) (SystemC-45FileC-45ReadWrite-fPutStr arg-1 eta-1 eta-2))) arg-3 eta-0)))))
(define PrimIO-unsafeCreateWorld (lambda (arg-1) (arg-1 #f)))
(define PrimIO-unsafePerformIO (lambda (arg-1) (PrimIO-unsafeCreateWorld (lambda (u--w) (let ((eff-0 (arg-1 u--w))) eff-0)))))
(define SystemC-45Errno-strerror (lambda (arg-0) (PrimIO-unsafePerformIO (lambda (eta-0) (SystemC-45Errno-prim__strerror arg-0 eta-0)))))
(define SystemC-45FileC-45Error-u--show_Show_FileError (lambda (arg-0) (case (vector-ref arg-0 0) ((0) (let ((e-0 (vector-ref arg-0 1))) (SystemC-45Errno-strerror e-0))) ((1) "File Read Error") ((2) "File Write Error") ((3) "File Not Found") ((4) "Permission Denied") (else "File Exists"))))
(define PolynomialRegression-case--caseC-32blockC-32inC-32main-11427 (lambda (arg-0 arg-1 arg-2 arg-3) (let ((e-2 (car arg-3))) (let ((e-3 (cdr arg-3))) (let ((u--cleanDegree (PolynomialRegression-getDatasetDegree "clean"))) (lambda (world-0) (let ((act-1 ((PolynomialRegression-trainPolynomialWithMonitoring u--cleanDegree (PolynomialRegression-initPolynomial u--cleanDegree) arg-1 PolynomialRegression-MAX_ITERATIONS) world-0))) (let ((u--predictions (PolynomialRegression-evaluatePolynomial u--cleanDegree act-1 e-2))) (let ((u--finalMSE (PolynomialRegression-computeMSE u--predictions e-3))) (let ((u--finalR2 (PolynomialRegression-computeR2 u--predictions e-3))) (let ((u--finalCoeffs (DataC-45Vect-u--toList_Foldable_C-40VectC-32C-36nC-41 (PolynomialRegression-getCoeffs u--cleanDegree act-1)))) (let ((u--coeffStr (DataC-45String-fastUnlines (PreludeC-45TypesC-45List-mapAppend '() (lambda (eta-0) (PreludeC-45Show-u--show_Show_Double eta-0)) u--finalCoeffs)))) (let ((act-2 ((SystemC-45FileC-45ReadWrite-writeFile csegen-13 "results/idris_poly_coefficients.txt" u--coeffStr) world-0))) (case (vector-ref act-2 0) ((1) (let ((u--metricsStr (string-append "Test MSE: " (string-append (PreludeC-45Show-u--show_Show_Double u--finalMSE) (string-append "\xa;Test R\xb2;: " (PreludeC-45Show-u--show_Show_Double u--finalR2)))))) (let ((act-3 ((SystemC-45FileC-45ReadWrite-writeFile csegen-13 "results/idris_performance_metrics.txt" u--metricsStr) world-0))) (case (vector-ref act-3 0) ((1) (let ((act-4 (PreludeC-45IO-prim__putStr "\xa; Type-safe polynomial regression complete!\xa;" world-0))) (PreludeC-45IO-prim__putStr " All datasets processed with bounded coefficients\xa;" world-0))) (else (let ((e-5 (vector-ref act-3 1))) (PreludeC-45IO-prim__putStr (string-append (string-append "  Warning: Could not save final metrics: " (SystemC-45FileC-45Error-u--show_Show_FileError e-5)) "\xa;") world-0))))))) (else (let ((e-5 (vector-ref act-2 1))) (PreludeC-45IO-prim__putStr (string-append (string-append "  Warning: Could not save final coefficients: " (SystemC-45FileC-45Error-u--show_Show_FileError e-5)) "\xa;") world-0)))))))))))))))))
(define PreludeC-45TypesC-45List-filterAppend (lambda (arg-1 arg-2 arg-3) (if (null? arg-3) (PreludeC-45TypesC-45SnocList-C-60C-62C-62 arg-1 '()) (let ((e-1 (car arg-3))) (let ((e-2 (cdr arg-3))) (let ((sc1 (arg-2 e-1))) (cond ((equal? sc1 1) (PreludeC-45TypesC-45List-filterAppend (cons arg-1 e-1) arg-2 e-2)) (else (PreludeC-45TypesC-45List-filterAppend arg-1 arg-2 e-2)))))))))
(define DataC-45String-n--3979-9380-u--linesHelp (lambda (arg-0 arg-1 arg-2) (if (null? arg-1) (if (null? arg-2) '() (if (null? arg-2) (cons (PreludeC-45TypesC-45List-reverse arg-1) '()) (let ((e-2 (car arg-2))) (let ((e-3 (cdr arg-2))) (cond ((equal? e-2 (integer->char 10)) (cons (PreludeC-45TypesC-45List-reverse arg-1) (DataC-45String-n--3979-9380-u--linesHelp arg-0 '() e-3))) ((equal? e-2 (integer->char 13)) (if (null? e-3) (cons (PreludeC-45TypesC-45List-reverse arg-1) (DataC-45String-n--3979-9380-u--linesHelp arg-0 '() e-3)) (let ((e-5 (car e-3))) (let ((e-6 (cdr e-3))) (cond ((equal? e-5 (integer->char 10)) (cons (PreludeC-45TypesC-45List-reverse arg-1) (DataC-45String-n--3979-9380-u--linesHelp arg-0 '() e-6)))(else (cons (PreludeC-45TypesC-45List-reverse arg-1) (DataC-45String-n--3979-9380-u--linesHelp arg-0 '() e-3))))))))(else (DataC-45String-n--3979-9380-u--linesHelp arg-0 (cons e-2 arg-1) e-3))))))) (if (null? arg-2) (cons (PreludeC-45TypesC-45List-reverse arg-1) '()) (let ((e-2 (car arg-2))) (let ((e-3 (cdr arg-2))) (cond ((equal? e-2 (integer->char 10)) (cons (PreludeC-45TypesC-45List-reverse arg-1) (DataC-45String-n--3979-9380-u--linesHelp arg-0 '() e-3))) ((equal? e-2 (integer->char 13)) (if (null? e-3) (cons (PreludeC-45TypesC-45List-reverse arg-1) (DataC-45String-n--3979-9380-u--linesHelp arg-0 '() e-3)) (let ((e-5 (car e-3))) (let ((e-6 (cdr e-3))) (cond ((equal? e-5 (integer->char 10)) (cons (PreludeC-45TypesC-45List-reverse arg-1) (DataC-45String-n--3979-9380-u--linesHelp arg-0 '() e-6)))(else (cons (PreludeC-45TypesC-45List-reverse arg-1) (DataC-45String-n--3979-9380-u--linesHelp arg-0 '() e-3))))))))(else (DataC-45String-n--3979-9380-u--linesHelp arg-0 (cons e-2 arg-1) e-3)))))))))
(define DataC-45String-linesC-39 (lambda (arg-0) (DataC-45String-n--3979-9380-u--linesHelp arg-0 '() arg-0)))
(define DataC-45String-lines (lambda (arg-0) (PreludeC-45TypesC-45List-mapAppend '() (lambda (eta-0) (PreludeC-45Types-fastPack eta-0)) (DataC-45String-linesC-39 (PreludeC-45Types-fastUnpack arg-0)))))
(define PreludeC-45TypesC-45List-mapMaybeAppend (lambda (arg-2 arg-3 arg-4) (if (null? arg-4) (PreludeC-45TypesC-45SnocList-C-60C-62C-62 arg-2 '()) (let ((e-1 (car arg-4))) (let ((e-2 (cdr arg-4))) (let ((sc1 (arg-3 e-1))) (if (null? sc1) (PreludeC-45TypesC-45List-mapMaybeAppend arg-2 arg-3 e-2) (let ((e-3 (unbox sc1))) (PreludeC-45TypesC-45List-mapMaybeAppend (cons arg-2 e-3) arg-3 e-2)))))))))
(define DataC-45String-null (lambda (ext-0) (PreludeC-45EqOrd-u--C-61C-61_Eq_String ext-0 "")))
(define DataC-45List1-forget (lambda (arg-1) (let ((e-1 (car arg-1))) (let ((e-2 (cdr arg-1))) (cons e-1 e-2)))))
(define DataC-45String-strM (lambda (arg-0) (cond ((equal? arg-0 "") '())(else (cons (string-ref arg-0 0) (substring arg-0 1 (string-length arg-0)))))))
(define DataC-45String-with--asList-9518 (lambda (arg-0 arg-1) (cond ((equal? arg-0 "") (if (null? arg-1) (vector 0 ) (let ((e-0 (car arg-1))) (let ((e-1 (cdr arg-1))) (vector 1 e-0 e-1 (lambda () (DataC-45String-asList e-1)))))))(else (let ((e-0 (car arg-1))) (let ((e-1 (cdr arg-1))) (vector 1 e-0 e-1 (lambda () (DataC-45String-asList e-1)))))))))
(define DataC-45String-asList (lambda (arg-0) (DataC-45String-with--asList-9518 arg-0 (DataC-45String-strM arg-0))))
(define PreludeC-45Types-isSpace (lambda (arg-0) (cond ((equal? arg-0 #\ ) 1) ((equal? arg-0 (integer->char 9)) 1) ((equal? arg-0 (integer->char 13)) 1) ((equal? arg-0 (integer->char 10)) 1) ((equal? arg-0 (integer->char 12)) 1) ((equal? arg-0 (integer->char 11)) 1) ((equal? arg-0 (integer->char 160)) 1)(else 0))))
(define DataC-45String-with--ltrim-9542 (lambda (arg-0 arg-1) (cond ((equal? arg-0 "") (case (vector-ref arg-1 0) ((0) "")(else (let ((e-0 (vector-ref arg-1 1))) (let ((e-1 (vector-ref arg-1 2))) (let ((e-2 (vector-ref arg-1 3))) (let ((u--str (string-cons e-0 e-1))) (let ((sc2 (PreludeC-45Types-isSpace e-0))) (cond ((equal? sc2 1) (DataC-45String-with--ltrim-9542 e-1 (e-2))) (else u--str))))))))))(else (let ((e-0 (vector-ref arg-1 1))) (let ((e-1 (vector-ref arg-1 2))) (let ((e-2 (vector-ref arg-1 3))) (let ((u--str (string-cons e-0 e-1))) (let ((sc1 (PreludeC-45Types-isSpace e-0))) (cond ((equal? sc1 1) (DataC-45String-with--ltrim-9542 e-1 (e-2))) (else u--str)))))))))))
(define DataC-45String-ltrim (lambda (arg-0) (DataC-45String-with--ltrim-9542 arg-0 (DataC-45String-asList arg-0))))
(define DataC-45String-trim (lambda (ext-0) (DataC-45String-ltrim (string-reverse (DataC-45String-ltrim (string-reverse ext-0))))))
(define PolynomialRegression-parseDoubleSimple (lambda (arg-0) (let ((u--trimmed (DataC-45String-trim arg-0))) (box (cast-string-double u--trimmed)))))
(define DataC-45List-span (lambda (arg-1 arg-2) (if (null? arg-2) (cons '() '()) (let ((e-2 (car arg-2))) (let ((e-3 (cdr arg-2))) (let ((sc1 (arg-1 e-2))) (cond ((equal? sc1 1) (let ((sc2 (DataC-45List-span arg-1 e-3))) (let ((e-5 (car sc2))) (let ((e-4 (cdr sc2))) (cons (cons e-2 e-5) e-4))))) (else (cons '() (cons e-2 e-3))))))))))
(define DataC-45List-break (lambda (arg-1 arg-2) (DataC-45List-span (lambda (eta-0) (let ((sc0 (arg-1 eta-0))) (cond ((equal? sc0 1) 0) (else 1)))) arg-2)))
(define DataC-45List1-singleton (lambda (arg-1) (cons arg-1 '())))
(define DataC-45List-split (lambda (arg-1 arg-2) (let ((sc0 (DataC-45List-break arg-1 arg-2))) (let ((e-2 (car sc0))) (let ((e-3 (cdr sc0))) (if (null? e-3) (DataC-45List1-singleton e-2) (let ((e-7 (cdr e-3))) (cons e-2 (DataC-45List1-forget (DataC-45List-split arg-1 e-7))))))))))
(define DataC-45List1-u--map_Functor_List1 (lambda (arg-2 arg-3) (let ((e-1 (car arg-3))) (let ((e-2 (cdr arg-3))) (cons (arg-2 e-1) (PreludeC-45TypesC-45List-mapAppend '() arg-2 e-2))))))
(define DataC-45String-split (lambda (arg-0 arg-1) (DataC-45List1-u--map_Functor_List1 (lambda (eta-0) (PreludeC-45Types-fastPack eta-0)) (DataC-45List-split arg-0 (PreludeC-45Types-fastUnpack arg-1)))))
(define PreludeC-45Types-u--C-62C-62C-61_Monad_Maybe (lambda (arg-2 arg-3) (if (null? arg-2) '() (let ((e-2 (unbox arg-2))) (arg-3 e-2)))))
(define PolynomialRegression-parseCSVLine (lambda (arg-0) (let ((sc0 (DataC-45List1-forget (DataC-45String-split (lambda (arg-1) (PreludeC-45EqOrd-u--C-61C-61_Eq_Char arg-1 #\,)) (DataC-45String-trim arg-0))))) (if (null? sc0) '() (let ((e-1 (car sc0))) (let ((e-2 (cdr sc0))) (if (null? e-2) '() (let ((e-4 (car e-2))) (let ((e-5 (cdr e-2))) (if (null? e-5) (PreludeC-45Types-u--C-62C-62C-61_Monad_Maybe (PolynomialRegression-parseDoubleSimple (DataC-45String-trim e-1)) (lambda (u--x) (PreludeC-45Types-u--C-62C-62C-61_Monad_Maybe (PolynomialRegression-parseDoubleSimple (DataC-45String-trim e-4)) (lambda (u--y) (box (cons u--x u--y)))))) '()))))))))))
(define PolynomialRegression-n--9015-10353-u--parseCSVContent (lambda (arg-0 arg-1) (let ((u--allLines (DataC-45String-lines arg-1))) (let ((u--dataLines (DataC-45List-drop 1 u--allLines))) (let ((u--validLines (PreludeC-45TypesC-45List-filterAppend '() (lambda (eta-0) (let ((sc0 (DataC-45String-null eta-0))) (cond ((equal? sc0 1) 0) (else 1)))) u--dataLines))) (PreludeC-45TypesC-45List-mapMaybeAppend '() (lambda (eta-0) (PolynomialRegression-parseCSVLine eta-0)) u--validLines))))))
(define Benchmark-estimateMemoryUsage (lambda (arg-0) (let ((u--dataMemory (/ (* (exact->inexact arg-0) 64.0) (* 1024.0 1024.0)))) (+ (+ 1.0 u--dataMemory) 0.5))))
(define DataC-45Fuel-forever (lambda () (box (lambda () (DataC-45Fuel-forever)))))
(define PreludeC-45Interfaces-C-42C-62 (lambda (arg-3 arg-4 arg-5) (let ((e-3 (vector-ref arg-3 2))) ((((e-3 'erased) 'erased) (((let ((eff-0 (let ((e-6 (vector-ref arg-3 0))) e-6))) (lambda (arg-0) (lambda (arg-1) ((((eff-0 'erased) 'erased) arg-0) arg-1)))) (lambda (eta-0) (lambda (eta-1) eta-1))) arg-4)) arg-5))))
(define SystemC-45FileC-45ReadWrite-fEOF (lambda (arg-1 arg-2) (let ((e-1 (car arg-1))) (let ((e-4 (vector-ref e-1 1))) ((((e-4 'erased) 'erased) (let ((e-6 (cdr arg-1))) ((e-6 'erased) (lambda (eta-0) (SystemC-45FileC-45ReadWrite-prim__eof arg-2 eta-0))))) (lambda (u--res) (let ((e-7 (car arg-1))) (let ((e-10 (vector-ref e-7 0))) (let ((e-12 (vector-ref e-10 1))) ((e-12 'erased) (PreludeC-45EqOrd-u--C-47C-61_Eq_Int u--res (blodwen-toSignedInt 0 63))))))))))))
(define SystemC-45FileC-45Error-fileError (lambda (arg-1 arg-2) (let ((e-1 (car arg-1))) (let ((e-4 (vector-ref e-1 1))) ((((e-4 'erased) 'erased) (let ((e-6 (cdr arg-1))) ((e-6 'erased) (lambda (eta-0) (SystemC-45FileC-45Error-prim__error arg-2 eta-0))))) (lambda (u--x) (let ((e-7 (car arg-1))) (let ((e-10 (vector-ref e-7 0))) (let ((e-12 (vector-ref e-10 1))) ((e-12 'erased) (PreludeC-45EqOrd-u--C-47C-61_Eq_Int u--x (blodwen-toSignedInt 0 63))))))))))))
(define SystemC-45FFI-free (lambda (arg-1 arg-2) (let ((e-2 (cdr arg-1))) ((e-2 'erased) (lambda (eta-0) (SystemC-45FFI-prim__free arg-2 eta-0))))))
(define PreludeC-45InterfacesC-45Applicative-u--pure_Applicative_ComposeC-40C-40C-46C-32C-36fC-41C-32C-36gC-41 (lambda (arg-3 arg-4 ext-0) (let ((e-2 (vector-ref arg-3 1))) ((e-2 'erased) (let ((e-5 (vector-ref arg-4 1))) ((e-5 'erased) ext-0))))))
(define SystemC-45FileC-45ReadWrite-getStringAndFree (lambda (arg-1 arg-2 arg-3) (let ((sc0 (PreludeC-45EqOrd-u--C-47C-61_Eq_Int (PrimIO-prim__nullAnyPtr arg-3) (blodwen-toSignedInt 0 63)))) (cond ((equal? sc0 1) (let ((e-1 (car arg-1))) (let ((e-4 (vector-ref e-1 1))) ((((e-4 'erased) 'erased) (SystemC-45FileC-45Error-fileError arg-1 arg-2)) (lambda (bind-0) (cond ((equal? bind-0 1) (SystemC-45FileC-45Error-returnError arg-1)) (else (PreludeC-45InterfacesC-45Applicative-u--pure_Applicative_ComposeC-40C-40C-46C-32C-36fC-41C-32C-36gC-41 (let ((e-7 (car arg-1))) (let ((e-10 (vector-ref e-7 0))) e-10)) (vector (lambda (u--b) (lambda (u--a) (lambda (u--func) (lambda (arg-8885) (case (vector-ref arg-8885 0) ((0) (let ((e-6 (vector-ref arg-8885 1))) (vector 0 e-6))) (else (let ((e-6 (vector-ref arg-8885 1))) (vector 1 (u--func e-6))))))))) (lambda (u--a) (lambda (arg-9931) (vector 1 arg-9931))) (lambda (u--b) (lambda (u--a) (lambda (arg-9937) (lambda (arg-9944) (case (vector-ref arg-9937 0) ((0) (let ((e-6 (vector-ref arg-9937 1))) (vector 0 e-6))) (else (let ((e-6 (vector-ref arg-9937 1))) (case (vector-ref arg-9944 0) ((1) (let ((e-8 (vector-ref arg-9944 1))) (vector 1 (e-6 e-8)))) (else (let ((e-11 (vector-ref arg-9944 1))) (vector 0 e-11)))))))))))) "")))))))) (else (let ((u--s (PreludeC-45IO-prim__getString arg-3))) (let ((e-1 (car arg-1))) (let ((e-4 (vector-ref e-1 1))) ((((e-4 'erased) 'erased) (SystemC-45FFI-free arg-1 arg-3)) (lambda (_-10649) (SystemC-45FileC-45Support-ok arg-1 u--s)))))))))))
(define SystemC-45FileC-45ReadWrite-fGetLine (lambda (arg-1 arg-2) (let ((e-1 (car arg-1))) (let ((e-4 (vector-ref e-1 1))) ((((e-4 'erased) 'erased) (let ((e-6 (cdr arg-1))) ((e-6 'erased) (lambda (eta-0) (SystemC-45FileC-45ReadWrite-prim__readLine arg-2 eta-0))))) (lambda (u--res) (SystemC-45FileC-45ReadWrite-getStringAndFree arg-1 arg-2 u--res)))))))
(define SystemC-45FileC-45ReadWrite-fSeekLine (lambda (arg-1 arg-2) (let ((e-1 (car arg-1))) (let ((e-4 (vector-ref e-1 1))) ((((e-4 'erased) 'erased) (let ((e-6 (cdr arg-1))) ((e-6 'erased) (lambda (eta-0) (SystemC-45FileC-45ReadWrite-prim__seekLine arg-2 eta-0))))) (lambda (u--res) (let ((sc1 (PreludeC-45EqOrd-u--C-47C-61_Eq_Int u--res (blodwen-toSignedInt 0 63)))) (cond ((equal? sc1 1) (SystemC-45FileC-45Error-returnError arg-1)) (else (SystemC-45FileC-45Support-ok arg-1 (vector 0 )))))))))))
(define PreludeC-45InterfacesC-45Applicative-u--C-60C-42C-62_Applicative_ComposeC-40C-40C-46C-32C-36fC-41C-32C-36gC-41 (lambda (arg-4 arg-5 arg-6 arg-7) (let ((e-3 (vector-ref arg-4 2))) ((((e-3 'erased) 'erased) (let ((e-4 (vector-ref arg-4 2))) ((((e-4 'erased) 'erased) (let ((e-8 (vector-ref arg-4 1))) ((e-8 'erased) (lambda (clam-0) (lambda (clam-1) (let ((e-10 (vector-ref arg-5 2))) ((((e-10 'erased) 'erased) clam-0) clam-1))))))) arg-6))) arg-7))))
(define PreludeC-45Types-u--C-62C-62C-61_Monad_C-40EitherC-32C-36eC-41 (lambda (arg-3 arg-4) (case (vector-ref arg-3 0) ((0) (let ((e-2 (vector-ref arg-3 1))) (vector 0 e-2))) (else (let ((e-5 (vector-ref arg-3 1))) (arg-4 e-5))))))
(define PreludeC-45InterfacesC-45Monad-u--C-62C-62C-61_Monad_ComposeC-40C-40C-46C-32C-36mC-41C-32C-36tC-41 (lambda (arg-4 arg-5 arg-6 arg-7 arg-8) (let ((e-2 (vector-ref arg-4 1))) ((((e-2 'erased) 'erased) arg-7) (lambda (eta-0) (((let ((eff-0 (let ((e-6 (vector-ref arg-4 0))) (let ((e-9 (vector-ref e-6 0))) e-9)))) (lambda (arg-0) (lambda (arg-1) ((((eff-0 'erased) 'erased) arg-0) arg-1)))) (lambda (clam-0) (let ((e-4 (vector-ref arg-5 2))) ((e-4 'erased) clam-0)))) (let ((e-4 (vector-ref arg-6 2))) ((((((e-4 'erased) 'erased) 'erased) (let ((e-9 (vector-ref arg-4 0))) e-9)) arg-8) eta-0))))))))
(define PreludeC-45Types-u--foldr_Foldable_C-40EitherC-32C-36eC-41 (lambda (arg-3 arg-4 arg-5) (case (vector-ref arg-5 0) ((0) arg-4) (else (let ((e-5 (vector-ref arg-5 1))) ((arg-3 e-5) arg-4))))))
(define PreludeC-45Types-u--foldMap_Foldable_C-40EitherC-32C-36eC-41 (lambda (arg-3 arg-4 ext-0) (PreludeC-45Types-u--foldr_Foldable_C-40EitherC-32C-36eC-41 (lambda (eta-0) (lambda (eta-1) (let ((e-1 (car arg-3))) ((e-1 (arg-4 eta-0)) eta-1)))) (let ((e-2 (cdr arg-3))) e-2) ext-0)))
(define PreludeC-45Types-u--foldl_Foldable_C-40EitherC-32C-36eC-41 (lambda (arg-3 arg-4 arg-5) ((PreludeC-45Types-u--foldr_Foldable_C-40EitherC-32C-36eC-41 (lambda (eta-0) (lambda (eta-1) (PreludeC-45Basics-flip (lambda (eta-2) (lambda (eta-3) (lambda (eta-4) (eta-2 (eta-3 eta-4))))) (lambda (eta-2) (PreludeC-45Basics-flip arg-3 eta-0 eta-2)) eta-1))) (lambda (eta-0) eta-0) arg-5) arg-4)))
(define PreludeC-45Types-u--foldlM_Foldable_C-40EitherC-32C-36eC-41 (lambda (arg-4 arg-5 arg-6 ext-0) (PreludeC-45Types-u--foldl_Foldable_C-40EitherC-32C-36eC-41 (lambda (u--ma) (lambda (u--b) (let ((e-2 (vector-ref arg-4 1))) ((((e-2 'erased) 'erased) u--ma) (lambda (eta-0) (PreludeC-45Basics-flip arg-5 u--b eta-0)))))) (let ((e-1 (vector-ref arg-4 0))) (let ((e-5 (vector-ref e-1 1))) ((e-5 'erased) arg-6))) ext-0)))
(define PreludeC-45Types-u--join_Monad_C-40EitherC-32C-36eC-41 (lambda (arg-2) (PreludeC-45Types-u--C-62C-62C-61_Monad_C-40EitherC-32C-36eC-41 arg-2 (lambda (eta-0) eta-0))))
(define PreludeC-45InterfacesC-45Functor-u--map_Functor_ComposeC-40C-40C-46C-32C-36fC-41C-32C-36gC-41 (lambda (arg-4 arg-5 ext-0 ext-1) ((((arg-4 'erased) 'erased) (lambda (arg-1) ((((arg-5 'erased) 'erased) ext-0) arg-1))) ext-1)))
(define PreludeC-45Types-u--null_Foldable_C-40EitherC-32C-36eC-41 (lambda (arg-2) (case (vector-ref arg-2 0) ((0) 1) (else 0))))
(define PreludeC-45Types-u--toList_Foldable_C-40EitherC-32C-36eC-41 (lambda (ext-0) (PreludeC-45Types-u--foldr_Foldable_C-40EitherC-32C-36eC-41 csegen-59 '() ext-0)))
(define PreludeC-45Types-u--traverse_Traversable_C-40EitherC-32C-36eC-41 (lambda (arg-4 arg-5 arg-6) (case (vector-ref arg-6 0) ((0) (let ((e-2 (vector-ref arg-6 1))) (let ((e-4 (vector-ref arg-4 1))) ((e-4 'erased) (vector 0 e-2))))) (else (let ((e-5 (vector-ref arg-6 1))) (let ((e-1 (vector-ref arg-4 0))) ((((e-1 'erased) 'erased) (lambda (eta-0) (vector 1 eta-0))) (arg-5 e-5))))))))
(define SystemC-45FileC-45ReadWrite-readLinesOnto (lambda (arg-1 arg-2 arg-3 arg-4 arg-5) (if (null? arg-4) (let ((e-1 (car arg-1))) (let ((e-5 (vector-ref e-1 0))) (let ((e-7 (vector-ref e-5 1))) ((e-7 'erased) (vector 1 (cons 0 (PreludeC-45TypesC-45List-reverse arg-2))))))) (let ((e-0 (unbox arg-4))) (let ((e-1 (car arg-1))) (let ((e-4 (vector-ref e-1 1))) ((((e-4 'erased) 'erased) (SystemC-45FileC-45ReadWrite-fEOF arg-1 arg-5)) (lambda (_-0) (cond ((equal? _-0 0) (cond ((equal? arg-3 0) (PreludeC-45InterfacesC-45Monad-u--C-62C-62C-61_Monad_ComposeC-40C-40C-46C-32C-36mC-41C-32C-36tC-41 (let ((e-7 (car arg-1))) e-7) (vector (vector (lambda (u--b) (lambda (u--a) (lambda (u--func) (lambda (arg-8885) (case (vector-ref arg-8885 0) ((0) (let ((e-6 (vector-ref arg-8885 1))) (vector 0 e-6))) (else (let ((e-6 (vector-ref arg-8885 1))) (vector 1 (u--func e-6))))))))) (lambda (u--a) (lambda (arg-9931) (vector 1 arg-9931))) (lambda (u--b) (lambda (u--a) (lambda (arg-9937) (lambda (arg-9944) (case (vector-ref arg-9937 0) ((0) (let ((e-6 (vector-ref arg-9937 1))) (vector 0 e-6))) (else (let ((e-6 (vector-ref arg-9937 1))) (case (vector-ref arg-9944 0) ((1) (let ((e-8 (vector-ref arg-9944 1))) (vector 1 (e-6 e-8)))) (else (let ((e-11 (vector-ref arg-9944 1))) (vector 0 e-11)))))))))))) (lambda (u--b) (lambda (u--a) (lambda (arg-10411) (lambda (arg-10414) (PreludeC-45Types-u--C-62C-62C-61_Monad_C-40EitherC-32C-36eC-41 arg-10411 arg-10414))))) (lambda (u--a) (lambda (arg-10425) (PreludeC-45Types-u--join_Monad_C-40EitherC-32C-36eC-41 arg-10425)))) (vector (lambda (u--b) (lambda (u--a) (lambda (u--func) (lambda (arg-8885) (case (vector-ref arg-8885 0) ((0) (let ((e-6 (vector-ref arg-8885 1))) (vector 0 e-6))) (else (let ((e-6 (vector-ref arg-8885 1))) (vector 1 (u--func e-6))))))))) (vector (lambda (u--acc) (lambda (u--elem) (lambda (u--func) (lambda (u--init) (lambda (u--input) (PreludeC-45Types-u--foldr_Foldable_C-40EitherC-32C-36eC-41 u--func u--init u--input)))))) (lambda (u--elem) (lambda (u--acc) (lambda (u--func) (lambda (u--init) (lambda (u--input) (PreludeC-45Types-u--foldl_Foldable_C-40EitherC-32C-36eC-41 u--func u--init u--input)))))) (lambda (u--elem) (lambda (arg-10911) (PreludeC-45Types-u--null_Foldable_C-40EitherC-32C-36eC-41 arg-10911))) (lambda (u--elem) (lambda (u--acc) (lambda (u--m) (lambda (i_con-0) (lambda (u--funcM) (lambda (u--init) (lambda (u--input) (PreludeC-45Types-u--foldlM_Foldable_C-40EitherC-32C-36eC-41 i_con-0 u--funcM u--init u--input)))))))) (lambda (u--elem) (lambda (arg-10940) (PreludeC-45Types-u--toList_Foldable_C-40EitherC-32C-36eC-41 arg-10940))) (lambda (u--a) (lambda (u--m) (lambda (i_con-0) (lambda (u--f) (lambda (arg-10954) (PreludeC-45Types-u--foldMap_Foldable_C-40EitherC-32C-36eC-41 i_con-0 u--f arg-10954))))))) (lambda (u--b) (lambda (u--a) (lambda (u--f) (lambda (i_con-0) (lambda (arg-14093) (lambda (arg-14100) (PreludeC-45Types-u--traverse_Traversable_C-40EitherC-32C-36eC-41 i_con-0 arg-14093 arg-14100)))))))) (SystemC-45FileC-45ReadWrite-fGetLine arg-1 arg-5) (lambda (u--str) (SystemC-45FileC-45ReadWrite-readLinesOnto arg-1 (cons u--str arg-2) 0 (e-0) arg-5))))(else (let ((e-6 (- arg-3 1))) (PreludeC-45Interfaces-C-42C-62 (vector (lambda (u--b) (lambda (u--a) (lambda (u--func) (lambda (arg-8885) (PreludeC-45InterfacesC-45Functor-u--map_Functor_ComposeC-40C-40C-46C-32C-36fC-41C-32C-36gC-41 (let ((e-8 (car arg-1))) (let ((e-11 (vector-ref e-8 0))) (let ((e-14 (vector-ref e-11 0))) e-14))) (lambda (b-0) (lambda (a-0) (lambda (func-0) (lambda (arg-8886) (case (vector-ref arg-8886 0) ((0) (let ((e-7 (vector-ref arg-8886 1))) (vector 0 e-7))) (else (let ((e-7 (vector-ref arg-8886 1))) (vector 1 (func-0 e-7))))))))) u--func arg-8885))))) (lambda (u--a) (lambda (arg-9931) (PreludeC-45InterfacesC-45Applicative-u--pure_Applicative_ComposeC-40C-40C-46C-32C-36fC-41C-32C-36gC-41 (let ((e-8 (car arg-1))) (let ((e-11 (vector-ref e-8 0))) e-11)) (vector (lambda (u--b) (lambda (a-0) (lambda (u--func) (lambda (arg-8885) (case (vector-ref arg-8885 0) ((0) (let ((e-7 (vector-ref arg-8885 1))) (vector 0 e-7))) (else (let ((e-7 (vector-ref arg-8885 1))) (vector 1 (u--func e-7))))))))) (lambda (a-0) (lambda (arg-9932) (vector 1 arg-9932))) (lambda (u--b) (lambda (a-0) (lambda (arg-9937) (lambda (arg-9944) (case (vector-ref arg-9937 0) ((0) (let ((e-7 (vector-ref arg-9937 1))) (vector 0 e-7))) (else (let ((e-7 (vector-ref arg-9937 1))) (case (vector-ref arg-9944 0) ((1) (let ((e-8 (vector-ref arg-9944 1))) (vector 1 (e-7 e-8)))) (else (let ((e-11 (vector-ref arg-9944 1))) (vector 0 e-11)))))))))))) arg-9931))) (lambda (u--b) (lambda (u--a) (lambda (arg-9937) (lambda (arg-9944) (PreludeC-45InterfacesC-45Applicative-u--C-60C-42C-62_Applicative_ComposeC-40C-40C-46C-32C-36fC-41C-32C-36gC-41 (let ((e-8 (car arg-1))) (let ((e-11 (vector-ref e-8 0))) e-11)) (vector (lambda (b-0) (lambda (a-0) (lambda (u--func) (lambda (arg-8885) (case (vector-ref arg-8885 0) ((0) (let ((e-7 (vector-ref arg-8885 1))) (vector 0 e-7))) (else (let ((e-7 (vector-ref arg-8885 1))) (vector 1 (u--func e-7))))))))) (lambda (a-0) (lambda (arg-9931) (vector 1 arg-9931))) (lambda (b-0) (lambda (a-0) (lambda (arg-9938) (lambda (arg-9945) (case (vector-ref arg-9938 0) ((0) (let ((e-7 (vector-ref arg-9938 1))) (vector 0 e-7))) (else (let ((e-7 (vector-ref arg-9938 1))) (case (vector-ref arg-9945 0) ((1) (let ((e-8 (vector-ref arg-9945 1))) (vector 1 (e-7 e-8)))) (else (let ((e-11 (vector-ref arg-9945 1))) (vector 0 e-11)))))))))))) arg-9937 arg-9944)))))) (SystemC-45FileC-45ReadWrite-fSeekLine arg-1 arg-5) (SystemC-45FileC-45ReadWrite-readLinesOnto arg-1 arg-2 e-6 (box e-0) arg-5)))))) (else (let ((e-7 (car arg-1))) (let ((e-10 (vector-ref e-7 0))) (let ((e-12 (vector-ref e-10 1))) ((e-12 'erased) (vector 1 (cons 1 (PreludeC-45TypesC-45List-reverse arg-2)))))))))))))))))
(define SystemC-45FileC-45ReadWrite-readFilePage (lambda (arg-1 arg-2 arg-3 arg-4) (SystemC-45FileC-45Handle-withFile arg-1 arg-4 0 (lambda (eta-0) (let ((e-1 (car arg-1))) (let ((e-5 (vector-ref e-1 0))) (let ((e-7 (vector-ref e-5 1))) ((e-7 'erased) eta-0))))) (lambda (eta-0) (SystemC-45FileC-45ReadWrite-readLinesOnto arg-1 '() arg-2 arg-3 eta-0)))))
(define SystemC-45FileC-45ReadWrite-readFile (lambda (arg-1 ext-0) (let ((e-1 (car arg-1))) (let ((e-5 (vector-ref e-1 0))) (let ((e-8 (vector-ref e-5 0))) ((((e-8 'erased) 'erased) (lambda (eta-0) (case (vector-ref eta-0 0) ((0) (let ((e-9 (vector-ref eta-0 1))) (vector 0 e-9))) (else (let ((e-9 (vector-ref eta-0 1))) (vector 1 (PreludeC-45Types-fastConcat (Builtin-snd e-9)))))))) (SystemC-45FileC-45ReadWrite-readFilePage arg-1 0 (DataC-45Fuel-forever) ext-0)))))))
(define PolynomialRegression-loadDataset (lambda (arg-0 ext-0) (let ((act-1 (PreludeC-45IO-prim__putStr (string-append (string-append "Loading dataset: " arg-0) "\xa;") ext-0))) (let ((act-2 ((SystemC-45FileC-45ReadWrite-readFile csegen-13 arg-0) ext-0))) (case (vector-ref act-2 0) ((1) (let ((e-2 (vector-ref act-2 1))) (let ((u--dataPoints (PolynomialRegression-n--9015-10353-u--parseCSVContent arg-0 e-2))) (let ((act-3 (PreludeC-45IO-prim__putStr (string-append (string-append "Loaded " (string-append (PreludeC-45Show-u--show_Show_Nat (PreludeC-45TypesC-45List-lengthTR u--dataPoints)) " data points")) "\xa;") ext-0))) (box u--dataPoints))))) (else (let ((e-5 (vector-ref act-2 1))) (let ((act-3 (PreludeC-45IO-prim__putStr (string-append (string-append "Error loading " (string-append arg-0 (string-append ": " (SystemC-45FileC-45Error-u--show_Show_FileError e-5)))) "\xa;") ext-0))) '()))))))))
(define SystemC-45Clock-isClockMandatory (lambda (arg-0) (cond ((equal? arg-0 5) 1) ((equal? arg-0 6) 1)(else 0))))
(define SystemC-45Clock-clockTimeGcCpu (lambda (ext-0) (SystemC-45Clock-prim__clockTimeGcCpu ext-0)))
(define SystemC-45Clock-clockTimeGcReal (lambda (ext-0) (SystemC-45Clock-prim__clockTimeGcReal ext-0)))
(define SystemC-45Clock-clockTimeMonotonic (lambda (ext-0) (SystemC-45Clock-prim__clockTimeMonotonic ext-0)))
(define SystemC-45Clock-clockTimeProcess (lambda (ext-0) (SystemC-45Clock-prim__clockTimeProcess ext-0)))
(define SystemC-45Clock-clockTimeThread (lambda (ext-0) (SystemC-45Clock-prim__clockTimeThread ext-0)))
(define SystemC-45Clock-clockTimeUtc (lambda (ext-0) (SystemC-45Clock-prim__clockTimeUtc ext-0)))
(define SystemC-45Clock-fetchOSClock (lambda (arg-0 ext-0) (cond ((equal? arg-0 0) (SystemC-45Clock-clockTimeUtc ext-0)) ((equal? arg-0 1) (SystemC-45Clock-clockTimeMonotonic ext-0)) ((equal? arg-0 3) (SystemC-45Clock-clockTimeProcess ext-0)) ((equal? arg-0 4) (SystemC-45Clock-clockTimeThread ext-0)) ((equal? arg-0 5) (SystemC-45Clock-clockTimeGcCpu ext-0)) ((equal? arg-0 6) (SystemC-45Clock-clockTimeGcReal ext-0)) (else (SystemC-45Clock-clockTimeMonotonic ext-0)))))
(define SystemC-45Clock-osClockNanosecond (lambda (arg-0 ext-0) (SystemC-45Clock-prim__osClockNanosecond arg-0 ext-0)))
(define SystemC-45Clock-osClockSecond (lambda (arg-0 ext-0) (SystemC-45Clock-prim__osClockSecond arg-0 ext-0)))
(define SystemC-45Clock-fromOSClock (lambda (arg-0 arg-1 ext-0) (let ((act-1 (SystemC-45Clock-osClockSecond arg-1 ext-0))) (let ((act-2 (SystemC-45Clock-osClockNanosecond arg-1 ext-0))) (vector arg-0 act-1 act-2)))))
(define SystemC-45Clock-osClockValid (lambda (arg-0 ext-0) (SystemC-45Clock-prim__osClockValid arg-0 ext-0)))
(define SystemC-45Clock-with--clockTime-4345 (lambda (arg-0 arg-1 ext-0) (cond ((equal? arg-1 0) (let ((act-1 (SystemC-45Clock-fetchOSClock arg-0 ext-0))) (SystemC-45Clock-fromOSClock arg-0 act-1 ext-0))) (else (let ((act-1 (SystemC-45Clock-fetchOSClock arg-0 ext-0))) (let ((act-2 (PreludeC-45IO-u--map_Functor_IO (lambda (arg-2) (PreludeC-45EqOrd-u--C-61C-61_Eq_Int arg-2 (blodwen-toSignedInt 1 63))) (lambda (eta-0) (SystemC-45Clock-osClockValid act-1 eta-0)) ext-0))) (cond ((equal? act-2 1) (PreludeC-45IO-u--map_Functor_IO (lambda (eta-0) (box eta-0)) (lambda (eta-0) (SystemC-45Clock-fromOSClock arg-0 act-1 eta-0)) ext-0)) (else '()))))))))
(define SystemC-45Clock-clockTime (lambda (arg-0 ext-0) (SystemC-45Clock-with--clockTime-4345 arg-0 (SystemC-45Clock-isClockMandatory arg-0) ext-0)))
(define SystemC-45Clock-nanoseconds (lambda (arg-1) (let ((e-2 (vector-ref arg-1 2))) e-2)))
(define SystemC-45Clock-seconds (lambda (arg-1) (let ((e-1 (vector-ref arg-1 1))) e-1)))
(define PreludeC-45Num-u--div_Integral_Integer (lambda (arg-0 arg-1) (let ((sc0 (PreludeC-45EqOrd-u--C-61C-61_Eq_Integer arg-1 0))) (cond ((equal? sc0 0) (blodwen-euclidDiv arg-0 arg-1))(else (blodwen-error-quit (string-append "ERROR: " "Unhandled input for Prelude.Num.case block in div at Prelude.Num:91:3--93:44")))))))
(define PreludeC-45Num-u--mod_Integral_Integer (lambda (arg-0 arg-1) (let ((sc0 (PreludeC-45EqOrd-u--C-61C-61_Eq_Integer arg-1 0))) (cond ((equal? sc0 0) (blodwen-euclidMod arg-0 arg-1))(else (blodwen-error-quit (string-append "ERROR: " "Unhandled input for Prelude.Num.case block in mod at Prelude.Num:94:3--96:44")))))))
(define SystemC-45Clock-fromNano (lambda (arg-0 arg-1) (let ((u--seconds (PreludeC-45Num-u--div_Integral_Integer arg-1 1000000000))) (let ((u--nanoseconds (PreludeC-45Num-u--mod_Integral_Integer arg-1 1000000000))) (vector arg-0 u--seconds u--nanoseconds)))))
(define SystemC-45Clock-toNano (lambda (arg-1) (let ((e-1 (vector-ref arg-1 1))) (let ((e-2 (vector-ref arg-1 2))) (+ (* e-1 1000000000) e-2)))))
(define SystemC-45Clock-timeDifference (lambda (arg-1 arg-2) (SystemC-45Clock-fromNano 2 (- (SystemC-45Clock-toNano arg-1) (SystemC-45Clock-toNano arg-2)))))
(define Benchmark-timeAction (lambda (arg-1 ext-0) (let ((act-1 (SystemC-45Clock-clockTime 1 ext-0))) (let ((act-2 (arg-1 ext-0))) (let ((act-3 (SystemC-45Clock-clockTime 1 ext-0))) (let ((u--duration (SystemC-45Clock-timeDifference act-3 act-1))) (let ((u--seconds (+ (exact->inexact (SystemC-45Clock-seconds u--duration)) (/ (exact->inexact (SystemC-45Clock-nanoseconds u--duration)) 1000000000.0)))) (cons act-2 u--seconds))))))))
(define PreludeC-45EqOrd-u--C-60C-61_Ord_Double (lambda (arg-0 arg-1) (let ((sc0 (or (and (<= arg-0 arg-1) 1) 0))) (cond ((equal? sc0 0) 0)(else 1)))))
(define PreludeC-45Types-u--C-62_Ord_Nat (lambda (arg-0 arg-1) (PreludeC-45EqOrd-u--C-61C-61_Eq_Ordering (PreludeC-45EqOrd-u--compare_Ord_Integer arg-0 arg-1) 2)))
(define PreludeC-45EqOrd-u--max_Ord_Double (lambda (arg-0 arg-1) (let ((sc0 (PreludeC-45EqOrd-u--C-62_Ord_Double arg-0 arg-1))) (cond ((equal? sc0 1) arg-0) (else arg-1)))))
(define PreludeC-45Show-u--show_Show_Bool (lambda (arg-0) (cond ((equal? arg-0 1) "True") (else "False"))))
(define DataC-45List-u--unzipWith_Zippable_List (lambda (arg-3 arg-4) (if (null? arg-4) (cons '() '()) (let ((e-2 (car arg-4))) (let ((e-3 (cdr arg-4))) (let ((sc1 (arg-3 e-2))) (let ((e-5 (car sc1))) (let ((e-4 (cdr sc1))) (let ((sc2 (DataC-45List-u--unzipWith_Zippable_List arg-3 e-3))) (let ((e-7 (car sc2))) (let ((e-6 (cdr sc2))) (cons (cons e-5 e-7) (cons e-4 e-6)))))))))))))
(define DataC-45List-u--unzip_Zippable_List (lambda (ext-0) (DataC-45List-u--unzipWith_Zippable_List (lambda (eta-0) eta-0) ext-0)))
(define PolynomialRegression-processDataset (lambda (arg-0 ext-0) (let ((act-1 (PreludeC-45IO-prim__putStr (string-append (string-append "\xa; Processing " (string-append arg-0 " dataset...")) "\xa;") ext-0))) (let ((act-2 (PolynomialRegression-loadDataset (string-append "datasets/" (string-append arg-0 "_train.csv")) ext-0))) (if (null? act-2) (let ((act-3 (PreludeC-45IO-prim__putStr (string-append (string-append "Failed to load " (string-append arg-0 " training data")) "\xa;") ext-0))) (vector 0 )) (let ((e-1 (unbox act-2))) (let ((act-3 (PolynomialRegression-loadDataset (string-append "datasets/" (string-append arg-0 "_test.csv")) ext-0))) (if (null? act-3) (let ((act-4 (PreludeC-45IO-prim__putStr (string-append (string-append "Failed to load " (string-append arg-0 " test data")) "\xa;") ext-0))) (vector 0 )) (let ((e-2 (unbox act-3))) (let ((sc2 (DataC-45List-u--unzip_Zippable_List e-1))) (let ((sc3 (DataC-45List-u--unzip_Zippable_List e-2))) (let ((e-6 (car sc3))) (let ((e-5 (cdr sc3))) (let ((act-4 (PreludeC-45IO-prim__putStr (string-append (string-append "Loaded " (string-append (PreludeC-45Show-u--show_Show_Nat (PreludeC-45TypesC-45List-lengthTR e-1)) (string-append " training samples, " (string-append (PreludeC-45Show-u--show_Show_Nat (PreludeC-45TypesC-45List-lengthTR e-2)) " test samples")))) "\xa;") ext-0))) (((let ((u--degree (PolynomialRegression-getDatasetDegree arg-0))) (let ((u--initialPoly (PolynomialRegression-initPolynomial u--degree))) (lambda () (lambda (world-0) (let ((act-5 (PreludeC-45IO-prim__putStr (string-append (string-append "Training polynomial (degree " (string-append (PreludeC-45Show-u--show_Show_Nat u--degree) (string-append ") on " (string-append arg-0 " dataset...")))) "\xa;") world-0))) (let ((act-6 (Benchmark-timeAction (PolynomialRegression-trainPolynomialWithMonitoring u--degree u--initialPoly e-1 PolynomialRegression-MAX_ITERATIONS) world-0))) (let ((e-8 (car act-6))) (let ((e-7 (cdr act-6))) (let ((u--memoryUsage (Benchmark-estimateMemoryUsage (+ (PreludeC-45TypesC-45List-lengthTR e-1) (PreludeC-45TypesC-45List-lengthTR e-2))))) (let ((u--predictions (PolynomialRegression-evaluatePolynomial u--degree e-8 e-6))) (let ((u--testRMSE (PolynomialRegression-computeRMSE u--degree e-8 e-2))) (let ((u--testR2 (PolynomialRegression-computeR2 u--predictions e-5))) (let ((act-7 (PreludeC-45IO-prim__putStr "Performance Metrics:\xa;" world-0))) (let ((act-8 (PreludeC-45IO-prim__putStr (string-append (string-append "   Test RMSE: " (PreludeC-45Show-u--show_Show_Double u--testRMSE)) "\xa;") world-0))) (let ((act-9 (PreludeC-45IO-prim__putStr (string-append (string-append "   Test R\xb2;: " (PreludeC-45Show-u--show_Show_Double u--testR2)) "\xa;") world-0))) (let ((act-10 (PreludeC-45IO-prim__putStr (string-append (string-append "   Execution Time: " (string-append (PreludeC-45Show-u--show_Show_Double e-7) "s")) "\xa;") world-0))) (let ((act-11 (PreludeC-45IO-prim__putStr (string-append (string-append "   Memory Usage: " (string-append (PreludeC-45Show-u--show_Show_Double u--memoryUsage) "MB")) "\xa;") world-0))) (((let ((u--coeffs (DataC-45Vect-u--toList_Foldable_C-40VectC-32C-36nC-41 (PolynomialRegression-getCoeffs u--degree e-8)))) (let ((u--maxCoeff (PreludeC-45Types-u--foldl_Foldable_List (lambda (eta-0) (lambda (eta-1) (PreludeC-45EqOrd-u--max_Ord_Double eta-0 eta-1))) 0.0 (PreludeC-45TypesC-45List-mapAppend '() (lambda (eta-0) (PreludeC-45Num-u--abs_Abs_Double eta-0)) u--coeffs)))) (let ((u--compliantCoeffs (PreludeC-45TypesC-45List-lengthTR (PreludeC-45TypesC-45List-filterAppend '() (lambda (u--c) (PreludeC-45EqOrd-u--C-60C-61_Ord_Double (PreludeC-45Num-u--abs_Abs_Double u--c) PolynomialRegression-MAX_COEFF)) u--coeffs)))) (let ((u--totalCoeffs (PreludeC-45TypesC-45List-lengthTR u--coeffs))) (let ((u--complianceRate (let ((sc5 (PreludeC-45Types-u--C-62_Ord_Nat u--totalCoeffs 0))) (cond ((equal? sc5 1) (* (/ (exact->inexact u--compliantCoeffs) (exact->inexact u--totalCoeffs)) 100.0)) (else 100.0))))) (lambda () (lambda (world-1) (let ((act-12 (PreludeC-45IO-prim__putStr (string-append (string-append " Max |coefficient|: " (PreludeC-45Show-u--show_Show_Double u--maxCoeff)) "\xa;") world-1))) (let ((act-13 (PreludeC-45IO-prim__putStr (string-append (string-append " Within bounds: " (PreludeC-45Show-u--show_Show_Bool (PreludeC-45EqOrd-u--C-60C-61_Ord_Double u--maxCoeff PolynomialRegression-MAX_COEFF))) "\xa;") world-1))) (let ((act-14 (PreludeC-45IO-prim__putStr (string-append (string-append " Bound compliance rate: " (string-append (PreludeC-45Show-u--show_Show_Double u--complianceRate) "%")) "\xa;") world-1))) (((let ((u--coeffStr (DataC-45String-fastUnlines (PreludeC-45TypesC-45List-mapAppend '() (lambda (eta-0) (PreludeC-45Show-u--show_Show_Double eta-0)) u--coeffs)))) (lambda () (lambda (world-2) (let ((act-15 ((SystemC-45FileC-45ReadWrite-writeFile csegen-13 (string-append "results/idris_" (string-append arg-0 "_coefficients.txt")) u--coeffStr) world-2))) (case (vector-ref act-15 0) ((1) (let ((u--metricsStr (string-append "Test RMSE: " (string-append (PreludeC-45Show-u--show_Show_Double u--testRMSE) (string-append "\xa;Test R\xb2;: " (string-append (PreludeC-45Show-u--show_Show_Double u--testR2) (string-append "\xa;Execution Time: " (string-append (PreludeC-45Show-u--show_Show_Double e-7) (string-append "\xa;Memory Usage: " (string-append (PreludeC-45Show-u--show_Show_Double u--memoryUsage) (string-append "\xa;Bound Compliance Rate: " (string-append (PreludeC-45Show-u--show_Show_Double u--complianceRate) "%")))))))))))) (let ((act-16 ((SystemC-45FileC-45ReadWrite-writeFile csegen-13 (string-append "results/idris_" (string-append arg-0 "_metrics.txt")) u--metricsStr) world-2))) (case (vector-ref act-16 0) ((1) (PreludeC-45IO-prim__putStr (string-append (string-append " Results saved for " (string-append arg-0 " dataset")) "\xa;") world-2)) (else (let ((e-10 (vector-ref act-16 1))) (PreludeC-45IO-prim__putStr (string-append (string-append "Warning: Could not save metrics: " (SystemC-45FileC-45Error-u--show_Show_FileError e-10)) "\xa;") world-2))))))) (else (let ((e-9 (vector-ref act-15 1))) (PreludeC-45IO-prim__putStr (string-append (string-append "Warning: Could not save coefficients: " (SystemC-45FileC-45Error-u--show_Show_FileError e-9)) "\xa;") world-2))))))))) world-1)))))))))))) world-0))))))))))))))))))) ext-0)))))))))))))))
(define System-system (lambda (arg-1 arg-2) (let ((e-2 (cdr arg-1))) ((e-2 'erased) (lambda (eta-0) (System-prim__system arg-2 eta-0))))))
(define PolynomialRegression-createResultsDir (lambda (ext-0) (let ((act-1 ((System-system csegen-13 "mkdir -p results") ext-0))) (vector 0 ))))
(define PreludeC-45Interfaces-traverse_ (lambda (arg-4 arg-5 arg-6 ext-0) (let ((e-1 (vector-ref arg-5 0))) (((((e-1 'erased) 'erased) (lambda (eta-0) (lambda (eta-1) (PreludeC-45Interfaces-C-42C-62 arg-4 (arg-6 eta-0) eta-1)))) (let ((e-8 (vector-ref arg-4 1))) ((e-8 'erased) (vector 0 )))) ext-0))))
(define PreludeC-45Interfaces-for_ (lambda (arg-4 arg-5 ext-0 ext-1) (PreludeC-45Basics-flip (lambda (eta-0) (lambda (eta-1) (PreludeC-45Interfaces-traverse_ arg-4 arg-5 eta-0 eta-1))) ext-0 ext-1)))
(define PolynomialRegression-main (lambda (ext-0) (let ((act-1 (PreludeC-45IO-prim__putStr " Type-Safe Polynomial Regression with Real Datasets\xa;" ext-0))) (let ((act-2 (PreludeC-45IO-prim__putStr "====================================================\xa;" ext-0))) (let ((act-3 (PolynomialRegression-createResultsDir ext-0))) (((let ((u--datasets (cons "clean" (cons "outliers" (cons "pathological" '()))))) (lambda () (lambda (world-0) (let ((act-4 ((PreludeC-45Interfaces-for_ csegen-6 csegen-32 u--datasets (lambda (eta-0) (lambda (eta-1) (PolynomialRegression-processDataset eta-0 eta-1)))) world-0))) (let ((act-5 (PolynomialRegression-loadDataset "datasets/clean_test.csv" world-0))) (if (null? act-5) (let ((act-6 (PreludeC-45IO-prim__putStr " Could not load clean dataset for final metrics\xa;" world-0))) (vector 0 )) (let ((e-1 (unbox act-5))) (let ((_-0 (box e-1))) ((PolynomialRegression-case--caseC-32blockC-32inC-32main-11427 u--datasets e-1 _-0 (DataC-45List-u--unzip_Zippable_List e-1)) world-0)))))))))) ext-0))))))
(define PreludeC-45EqOrd-compareInteger (lambda (ext-0 ext-1) (PreludeC-45EqOrd-u--compare_Ord_Integer ext-0 ext-1)))
(collect-request-handler (lambda () (collect) (blodwen-run-finalisers)))
(PrimIO-unsafePerformIO (lambda (eta-0) (PolynomialRegression-main eta-0)))
  (collect 4)
  (blodwen-run-finalisers)
  
  )