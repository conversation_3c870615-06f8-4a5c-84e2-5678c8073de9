#!/usr/local/bin/chez --program

;; @generated by Idris 0.7.0, Chez backend
(import (chezscheme))
(case (machine-type)
  [(i3fb ti3fb a6fb ta6fb) #f]
  [(i3le ti3le a6le ta6le tarm64le) (load-shared-object "libc.so.6")]
  [(i3osx ti3osx a6osx ta6osx tarm64osx) (load-shared-object "libc.dylib")]
  [(i3nt ti3nt a6nt ta6nt) (load-shared-object "msvcrt.dll")]
  [else (load-shared-object "libc.so")])

(load-shared-object "libidris2_support.dylib")

(let ()
(define (blodwen-os)
  (case (machine-type)
    [(i3le ti3le a6le ta6le tarm64le) "unix"]  ; GNU/Linux
    [(i3ob ti3ob a6ob ta6ob tarm64ob) "unix"]  ; OpenBSD
    [(i3fb ti3fb a6fb ta6fb tarm64fb) "unix"]  ; FreeBSD
    [(i3nb ti3nb a6nb ta6nb tarm64nb) "unix"]  ; NetBSD
    [(i3osx ti3osx a6osx ta6osx tarm64osx) "darwin"]
    [(i3nt ti3nt a6nt ta6nt tarm64nt) "windows"]
    [else "unknown"]))

(define blodwen-lazy
  (lambda (f)
    (let ([evaluated #f] [res void])
      (lambda ()
        (if (not evaluated)
            (begin (set! evaluated #t)
                   (set! res (f))
                   (set! f void))
            (void))
        res))))

(define (blodwen-toSignedInt x bits)
  (if (logbit? bits x)
      (logor x (ash -1 bits))
      (logand x (sub1 (ash 1 bits)))))

(define (blodwen-toUnsignedInt x bits)
  (logand x (sub1 (ash 1 bits))))

(define (blodwen-euclidDiv a b)
  (let ((q (quotient a b))
        (r (remainder a b)))
    (if (< r 0)
      (if (> b 0) (- q 1) (+ q 1))
      q)))

(define (blodwen-euclidMod a b)
  (let ((r (remainder a b)))
    (if (< r 0)
      (if (> b 0) (+ r b) (- r b))
      r)))

; flonum constants

(define (blodwen-calcFlonumUnitRoundoff)
  (let loop [(uro 1.0)]
    (if (fl= 1.0 (fl+ 1.0 uro))
      uro
      (loop (fl/ uro 2.0)))))

(define (blodwen-calcFlonumEpsilon)
  (fl* (blodwen-calcFlonumUnitRoundoff) 2.0))

(define (blodwen-flonumNaN)
  +nan.0)

(define (blodwen-flonumInf)
  +inf.0)

; Bits

(define bu+ (lambda (x y bits) (blodwen-toUnsignedInt (+ x y) bits)))
(define bu- (lambda (x y bits) (blodwen-toUnsignedInt (- x y) bits)))
(define bu* (lambda (x y bits) (blodwen-toUnsignedInt (* x y) bits)))
(define bu/ (lambda (x y bits) (blodwen-toUnsignedInt (quotient x y) bits)))

(define bs+ (lambda (x y bits) (blodwen-toSignedInt (+ x y) bits)))
(define bs- (lambda (x y bits) (blodwen-toSignedInt (- x y) bits)))
(define bs* (lambda (x y bits) (blodwen-toSignedInt (* x y) bits)))
(define bs/ (lambda (x y bits) (blodwen-toSignedInt (blodwen-euclidDiv x y) bits)))

(define (integer->bits8 x) (logand x (sub1 (ash 1 8))))
(define (integer->bits16 x) (logand x (sub1 (ash 1 16))))
(define (integer->bits32 x) (logand x (sub1 (ash 1 32))))
(define (integer->bits64 x) (logand x (sub1 (ash 1 64))))

(define (bits16->bits8 x) (logand x (sub1 (ash 1 8))))
(define (bits32->bits8 x) (logand x (sub1 (ash 1 8))))
(define (bits64->bits8 x) (logand x (sub1 (ash 1 8))))
(define (bits32->bits16 x) (logand x (sub1 (ash 1 16))))
(define (bits64->bits16 x) (logand x (sub1 (ash 1 16))))
(define (bits64->bits32 x) (logand x (sub1 (ash 1 32))))

(define (blodwen-bits-shl-signed x y bits) (blodwen-toSignedInt (ash x y) bits))

(define (blodwen-bits-shl x y bits) (logand (ash x y) (sub1 (ash 1 bits))))

(define blodwen-shl (lambda (x y) (ash x y)))
(define blodwen-shr (lambda (x y) (ash x (- y))))
(define blodwen-and (lambda (x y) (logand x y)))
(define blodwen-or (lambda (x y) (logor x y)))
(define blodwen-xor (lambda (x y) (logxor x y)))

(define cast-num
  (lambda (x)
    (if (number? x) x 0)))
(define destroy-prefix
  (lambda (x)
    (cond
      ((equal? x "") "")
      ((equal? (string-ref x 0) #\#) "")
      (else x))))

(define exact-floor
  (lambda (x)
    (inexact->exact (floor x))))

(define exact-truncate
  (lambda (x)
    (inexact->exact (truncate x))))

(define exact-truncate-boundedInt
  (lambda (x y)
    (blodwen-toSignedInt (exact-truncate x) y)))

(define exact-truncate-boundedUInt
  (lambda (x y)
    (blodwen-toUnsignedInt (exact-truncate x) y)))

(define cast-char-boundedInt
  (lambda (x y)
    (blodwen-toSignedInt (char->integer x) y)))

(define cast-char-boundedUInt
  (lambda (x y)
    (blodwen-toUnsignedInt (char->integer x) y)))

(define cast-string-int
  (lambda (x)
    (exact-truncate (cast-num (string->number (destroy-prefix x))))))

(define cast-string-boundedInt
  (lambda (x y)
    (blodwen-toSignedInt (cast-string-int x) y)))

(define cast-string-boundedUInt
  (lambda (x y)
    (blodwen-toUnsignedInt (cast-string-int x) y)))

(define cast-int-char
  (lambda (x)
    (if (or
          (and (>= x 0) (<= x #xd7ff))
          (and (>= x #xe000) (<= x #x10ffff)))
        (integer->char x)
        (integer->char 0))))

(define cast-string-double
  (lambda (x)
    (exact->inexact (cast-num (string->number (destroy-prefix x))))))


(define (string-concat xs) (apply string-append xs))
(define (string-unpack s) (string->list s))
(define (string-pack xs) (list->string xs))

(define string-cons (lambda (x y) (string-append (string x) y)))
(define string-reverse (lambda (x)
  (list->string (reverse (string->list x)))))
(define (string-substr off len s)
    (let* ((l (string-length s))
          (b (max 0 off))
          (x (max 0 len))
          (end (min l (+ b x))))
          (if (> b l)
              ""
              (substring s b end))))

(define (blodwen-string-iterator-new s)
  0)

(define (blodwen-string-iterator-to-string _ s ofs f)
  (f (substring s ofs (string-length s))))

(define (blodwen-string-iterator-next s ofs)
  (if (>= ofs (string-length s))
      '() ; EOF
      (cons (string-ref s ofs) (+ ofs 1))))

(define either-left
  (lambda (x)
    (vector 0 x)))

(define either-right
  (lambda (x)
    (vector 1 x)))

(define blodwen-error-quit
  (lambda (msg)
    (display msg)
    (newline)
    (exit 1)))

(define (blodwen-get-line p)
    (if (port? p)
        (let ((str (get-line p)))
            (if (eof-object? str)
                ""
                str))
        void))

(define (blodwen-get-char p)
    (if (port? p)
        (let ((chr (get-char p)))
            (if (eof-object? chr)
                #\nul
                chr))
        void))

;; Buffers

(define (blodwen-new-buffer size)
  (make-bytevector size 0))

(define (blodwen-buffer-size buf)
  (bytevector-length buf))

(define (blodwen-buffer-setbyte buf loc val)
  (bytevector-u8-set! buf loc val))

(define (blodwen-buffer-getbyte buf loc)
  (bytevector-u8-ref buf loc))

(define (blodwen-buffer-setbits16 buf loc val)
  (bytevector-u16-set! buf loc val (native-endianness)))

(define (blodwen-buffer-getbits16 buf loc)
  (bytevector-u16-ref buf loc (native-endianness)))

(define (blodwen-buffer-setbits32 buf loc val)
  (bytevector-u32-set! buf loc val (native-endianness)))

(define (blodwen-buffer-getbits32 buf loc)
  (bytevector-u32-ref buf loc (native-endianness)))

(define (blodwen-buffer-setbits64 buf loc val)
  (bytevector-u64-set! buf loc val (native-endianness)))

(define (blodwen-buffer-getbits64 buf loc)
  (bytevector-u64-ref buf loc (native-endianness)))

(define (blodwen-buffer-setint8 buf loc val)
  (bytevector-s8-set! buf loc val))

(define (blodwen-buffer-getint8 buf loc)
  (bytevector-s8-ref buf loc))

(define (blodwen-buffer-setint16 buf loc val)
  (bytevector-s16-set! buf loc val (native-endianness)))

(define (blodwen-buffer-getint16 buf loc)
  (bytevector-s16-ref buf loc (native-endianness)))

(define (blodwen-buffer-setint32 buf loc val)
  (bytevector-s32-set! buf loc val (native-endianness)))

(define (blodwen-buffer-getint32 buf loc)
  (bytevector-s32-ref buf loc (native-endianness)))

(define (blodwen-buffer-setint buf loc val)
  (bytevector-s64-set! buf loc val (native-endianness)))

(define (blodwen-buffer-getint buf loc)
  (bytevector-s64-ref buf loc (native-endianness)))

(define (blodwen-buffer-setint64 buf loc val)
  (bytevector-s64-set! buf loc val (native-endianness)))

(define (blodwen-buffer-getint64 buf loc)
  (bytevector-s64-ref buf loc (native-endianness)))

(define (blodwen-buffer-setdouble buf loc val)
  (bytevector-ieee-double-set! buf loc val (native-endianness)))

(define (blodwen-buffer-getdouble buf loc)
  (bytevector-ieee-double-ref buf loc (native-endianness)))

(define (blodwen-stringbytelen str)
  (bytevector-length (string->utf8 str)))

(define (blodwen-buffer-setstring buf loc val)
  (let* [(strvec (string->utf8 val))
         (len (bytevector-length strvec))]
    (bytevector-copy! strvec 0 buf loc len)))

(define (blodwen-buffer-getstring buf loc len)
  (let [(newvec (make-bytevector len))]
    (bytevector-copy! buf loc newvec 0 len)
    (utf8->string newvec)))

(define (blodwen-buffer-copydata buf start len dest loc)
  (bytevector-copy! buf start dest loc len))

;; Threads

(define-record thread-handle (semaphore))

(define (blodwen-thread proc)
  (let [(sema (blodwen-make-semaphore 0))]
    (fork-thread (lambda () (proc (vector 0)) (blodwen-semaphore-post sema)))
    (make-thread-handle sema)
    ))

(define (blodwen-thread-wait handle)
  (blodwen-semaphore-wait (thread-handle-semaphore handle)))

;; Thread mailboxes

(define blodwen-thread-data
  (make-thread-parameter #f))

(define (blodwen-get-thread-data ty)
  (blodwen-thread-data))

(define (blodwen-set-thread-data ty a)
  (blodwen-thread-data a))

;; Semaphore

(define-record semaphore (box mutex condition))

(define (blodwen-make-semaphore init)
  (make-semaphore (box init) (make-mutex) (make-condition)))

(define (blodwen-semaphore-post sema)
  (with-mutex (semaphore-mutex sema)
    (let [(sema-box (semaphore-box sema))]
      (set-box! sema-box (+ (unbox sema-box) 1))
      (condition-signal (semaphore-condition sema))
    )))

(define (blodwen-semaphore-wait sema)
  (with-mutex (semaphore-mutex sema)
    (let [(sema-box (semaphore-box sema))]
      (when (= (unbox sema-box) 0)
        (condition-wait (semaphore-condition sema) (semaphore-mutex sema)))
      (set-box! sema-box (- (unbox sema-box) 1))
      )))

;; Barrier

(define-record barrier (count-box num-threads mutex cond))

(define (blodwen-make-barrier num-threads)
  (make-barrier (box 0) num-threads (make-mutex) (make-condition)))

(define (blodwen-barrier-wait barrier)
  (let [(count-box (barrier-count-box barrier))
        (num-threads (barrier-num-threads barrier))
        (mutex (barrier-mutex barrier))
        (condition (barrier-cond barrier))]
    (with-mutex mutex
    (let* [(count-old (unbox count-box))
           (count-new (+ count-old 1))]
      (set-box! count-box count-new)
      (if (= count-new num-threads)
          (condition-broadcast condition)
          (condition-wait condition mutex))
      ))))

;; Channel
; With thanks to Alain Zscheile (@zseri) for help with understanding condition
; variables, and figuring out where the problems were and how to solve them.

(define-record channel (read-mut read-cv read-box val-cv val-box))

(define (blodwen-make-channel ty)
  (make-channel
    (make-mutex)
    (make-condition)
    (box #t)
    (make-condition)
    (box '())
    ))

; block on the read status using read-cv until the value has been read
(define (channel-put-while-helper chan)
  (let ([read-mut (channel-read-mut chan)]
        [read-box (channel-read-box chan)]
        [read-cv  (channel-read-cv  chan)]
        )
    (if (unbox read-box)
      (void)    ; val has been read, so everything is fine
      (begin    ; otherwise, block/spin with cv
        (condition-wait read-cv read-mut)
        (channel-put-while-helper chan)
        )
      )))

(define (blodwen-channel-put ty chan val)
  (with-mutex (channel-read-mut chan)
    (channel-put-while-helper chan)
    (let ([read-box (channel-read-box chan)]
          [val-box  (channel-val-box  chan)]
          )
      (set-box! val-box val)
      (set-box! read-box #f)
      ))
  (condition-signal (channel-val-cv chan))
  )

; block on the value until it has been set
(define (channel-get-while-helper chan)
  (let ([read-mut (channel-read-mut chan)]
        [read-box (channel-read-box chan)]
        [val-cv   (channel-val-cv   chan)]
        )
    (if (unbox read-box)
      (begin
        (condition-wait val-cv read-mut)
        (channel-get-while-helper chan)
        )
      (void)
      )))

(define (blodwen-channel-get ty chan)
  (mutex-acquire (channel-read-mut chan))
  (channel-get-while-helper chan)
  (let* ([val-box  (channel-val-box  chan)]
         [read-box (channel-read-box chan)]
         [read-cv  (channel-read-cv  chan)]
         [the-val  (unbox val-box)]
         )
    (set-box! val-box '())
    (set-box! read-box #t)
    (mutex-release (channel-read-mut chan))
    (condition-signal read-cv)
    the-val))

;; Mutex

(define (blodwen-make-mutex)
  (make-mutex))
(define (blodwen-mutex-acquire mutex)
  (mutex-acquire mutex))
(define (blodwen-mutex-release mutex)
  (mutex-release mutex))

;; Condition variable

(define (blodwen-make-condition)
  (make-condition))
(define (blodwen-condition-wait condition mutex)
  (condition-wait condition mutex))
(define (blodwen-condition-wait-timeout condition mutex timeout)
  (let* [(sec (div timeout 1000000))
         (micro (mod timeout 1000000))]
    (condition-wait condition mutex (make-time 'time-duration (* 1000 micro) sec))))
(define (blodwen-condition-signal condition)
  (condition-signal condition))
(define (blodwen-condition-broadcast condition)
  (condition-broadcast condition))

;; Future

(define-record future-internal (result ready mutex signal))
(define (blodwen-make-future work)
  (let ([future (make-future-internal #f #f (make-mutex) (make-condition))])
    (fork-thread (lambda ()
      (let ([result (work)])
        (with-mutex (future-internal-mutex future)
          (set-future-internal-result! future result)
          (set-future-internal-ready! future #t)
          (condition-broadcast (future-internal-signal future))))))
    future))
(define (blodwen-await-future ty future)
  (let ([mutex (future-internal-mutex future)])
    (with-mutex mutex
      (if (not (future-internal-ready future))
          (condition-wait (future-internal-signal future) mutex))
      (future-internal-result future))))

(define (blodwen-sleep s) (sleep (make-time 'time-duration 0 s)))
(define (blodwen-usleep s)
  (let ((sec (div s 1000000))
        (micro (mod s 1000000)))
       (sleep (make-time 'time-duration (* 1000 micro) sec))))

(define (blodwen-clock-time-utc) (current-time 'time-utc))
(define (blodwen-clock-time-monotonic) (current-time 'time-monotonic))
(define (blodwen-clock-time-duration) (current-time 'time-duration))
(define (blodwen-clock-time-process) (current-time 'time-process))
(define (blodwen-clock-time-thread) (current-time 'time-thread))
(define (blodwen-clock-time-gccpu) (current-time 'time-collector-cpu))
(define (blodwen-clock-time-gcreal) (current-time 'time-collector-real))
(define (blodwen-is-time? clk) (if (time? clk) 1 0))
(define (blodwen-clock-second time) (time-second time))
(define (blodwen-clock-nanosecond time) (time-nanosecond time))


(define (blodwen-arg-count)
  (length (command-line)))

(define (blodwen-arg n)
  (if (< n (length (command-line))) (list-ref (command-line) n) ""))

(define (blodwen-hasenv var)
  (if (eq? (getenv var) #f) 0 1))

;; Randoms
(define random-seed-register 0)
(define (initialize-random-seed-once)
  (if (= (virtual-register random-seed-register) 0)
      (let ([seed (time-nanosecond (current-time))])
        (set-virtual-register! random-seed-register seed)
        (random-seed seed))))

(define (blodwen-random-seed seed)
  (set-virtual-register! random-seed-register seed)
  (random-seed seed))
(define blodwen-random
  (case-lambda
    ;; no argument, pick a real value from [0, 1.0)
    [() (begin
          (initialize-random-seed-once)
          (random 1.0))]
    ;; single argument k, pick an integral value from [0, k)
    [(k)
      (begin
        (initialize-random-seed-once)
        (if (> k 0)
              (random k)
              (assertion-violationf 'blodwen-random "invalid range argument ~a" k)))]))

;; For finalisers

(define blodwen-finaliser (make-guardian))
(define (blodwen-register-object obj proc)
  (let [(x (cons obj proc))]
       (blodwen-finaliser x)
       x))
(define blodwen-run-finalisers
  (lambda ()
    (let run ()
      (let ([x (blodwen-finaliser)])
        (when x
          (((cdr x) (car x)) 'erased)
          (run))))))

;; For creating and reading back scheme objects

; read a scheme string and evaluate it, returning 'Just result' on success
; TODO: catch exception!
(define (blodwen-eval-scheme str)
  (guard
     (x [#t '()]) ; Nothing on failure
     (box (eval (read (open-input-string str)))))
  ); box == Just

(define (blodwen-eval-okay obj)
  (if (null? obj)
      0
      1))

(define (blodwen-get-eval-result obj)
  (unbox obj))

(define (blodwen-debug-scheme obj)
  (display obj) (newline))

(define (blodwen-is-number obj)
  (if (number? obj) 1 0))

(define (blodwen-is-integer obj)
  (if (and (number? obj) (exact? obj)) 1 0))

(define (blodwen-is-float obj)
  (if (flonum? obj) 1 0))

(define (blodwen-is-char obj)
  (if (char? obj) 1 0))

(define (blodwen-is-string obj)
  (if (string? obj) 1 0))

(define (blodwen-is-procedure obj)
  (if (procedure? obj) 1 0))

(define (blodwen-is-symbol obj)
  (if (symbol? obj) 1 0))

(define (blodwen-is-vector obj)
  (if (vector? obj) 1 0))

(define (blodwen-is-nil obj)
  (if (null? obj) 1 0))

(define (blodwen-is-pair obj)
  (if (pair? obj) 1 0))

(define (blodwen-is-box obj)
  (if (box? obj) 1 0))

(define (blodwen-make-symbol str)
  (string->symbol str))

; The below rely on checking that the objects are the right type first.

(define (blodwen-vector-ref obj i)
  (vector-ref obj i))

(define (blodwen-vector-length obj)
  (vector-length obj))

(define (blodwen-vector-list obj)
  (vector->list obj))

(define (blodwen-unbox obj)
  (unbox obj))

(define (blodwen-apply obj arg)
  (obj arg))

(define (blodwen-force obj)
  (obj))

(define (blodwen-read-symbol sym)
  (symbol->string sym))

(define (blodwen-id x) x)
(define PreludeC-45Types-fastUnpack (lambda (farg-0) (string-unpack farg-0)))
(define PreludeC-45IO-prim__putStr (lambda (farg-0 farg-1) ((foreign-procedure "idris2_putStr" (string) void) farg-0)))
(define csegen-22 (lambda (eta-0) (lambda (eta-1) (+ eta-0 eta-1))))
(define csegen-23 (lambda (eta-0) (lambda (eta-1) (- eta-0 eta-1))))
(define u--prim__sub_Integer (lambda (arg-0 arg-1) (- arg-0 arg-1)))
(define PreludeC-45TypesC-45SnocList-C-60C-62C-62 (lambda (arg-1 arg-2) (if (null? arg-1) arg-2 (let ((e-2 (car arg-1))) (let ((e-3 (cdr arg-1))) (PreludeC-45TypesC-45SnocList-C-60C-62C-62 e-2 (cons e-3 arg-2)))))))
(define PreludeC-45TypesC-45List-mapAppend (lambda (arg-2 arg-3 arg-4) (if (null? arg-4) (PreludeC-45TypesC-45SnocList-C-60C-62C-62 arg-2 '()) (let ((e-1 (car arg-4))) (let ((e-2 (cdr arg-4))) (PreludeC-45TypesC-45List-mapAppend (cons arg-2 (arg-3 e-1)) arg-3 e-2))))))
(define DataC-45List-u--zipWith_Zippable_List (lambda (arg-3 arg-4 arg-5) (if (null? arg-4) '() (if (null? arg-5) '() (let ((e-1 (car arg-4))) (let ((e-2 (cdr arg-4))) (let ((e-4 (car arg-5))) (let ((e-5 (cdr arg-5))) (cons ((arg-3 e-1) e-4) (DataC-45List-u--zipWith_Zippable_List arg-3 e-2 e-5))))))))))
(define DataC-45List-u--zip_Zippable_List (lambda (ext-0 ext-1) (DataC-45List-u--zipWith_Zippable_List (lambda (__leftTupleSection-0) (lambda (__infixTupleSection-0) (cons __leftTupleSection-0 __infixTupleSection-0))) ext-0 ext-1)))
(define OptimizedPolynomial-n--6897-6948-u--generatePathologicalDataOpt (lambda (arg-0) (let ((u--xs (cons (- 1.0) (cons (- 0.5) (cons 0.0 (cons 0.5 (cons 1.0 '()))))))) (let ((u--ys (PreludeC-45TypesC-45List-mapAppend '() (lambda (u--x) (- (+ (- 1.0 (* (* 2.5 u--x) u--x)) (* (* (* (* 1.8 u--x) u--x) u--x) u--x)) (* (* (* (* (* 0.3 u--x) u--x) u--x) u--x) u--x))) u--xs))) (DataC-45List-u--zip_Zippable_List u--xs u--ys)))))
(define OptimizedPolynomial-n--6897-6947-u--generateOutliersDataOpt (lambda (arg-0) (let ((u--xs (cons (- 2.0) (cons (- 1.0) (cons 0.0 (cons 1.0 (cons 2.0 '()))))))) (let ((u--ys (PreludeC-45TypesC-45List-mapAppend '() (lambda (u--x) (+ (+ (- 0.2 (* 1.5 u--x)) (* (* 0.8 u--x) u--x)) (* (* (* 1.2 u--x) u--x) u--x))) u--xs))) (DataC-45List-u--zip_Zippable_List u--xs u--ys)))))
(define OptimizedPolynomial-n--6897-6946-u--generateCleanDataOpt (lambda (arg-0) (let ((u--xs (cons (- 2.0) (cons (- 1.5) (cons (- 1.0) (cons (- 0.5) (cons 0.0 (cons 0.5 (cons 1.0 (cons 1.5 (cons 2.0 '()))))))))))) (let ((u--ys (PreludeC-45TypesC-45List-mapAppend '() (lambda (u--x) (+ (+ (- 0.5) (* 1.8 u--x)) (* (* 0.9 u--x) u--x))) u--xs))) (DataC-45List-u--zip_Zippable_List u--xs u--ys)))))
(define OptimizedPolynomial-n--5461-5632-u--evalPolyHelper (lambda (arg-1 arg-2 arg-3 arg-4 arg-5) (if (null? arg-3) 0.0 (let ((e-2 (car arg-3))) (let ((e-3 (cdr arg-3))) (+ (* e-2 (flexpt arg-4 (exact->inexact arg-5))) (OptimizedPolynomial-n--5461-5632-u--evalPolyHelper arg-1 arg-2 e-3 arg-4 (+ arg-5 1))))))))
(define OptimizedPolynomial-MAX_COEFF 2.0)
(define PreludeC-45EqOrd-u--C-60_Ord_Double (lambda (arg-0 arg-1) (let ((sc0 (or (and (< arg-0 arg-1) 1) 0))) (cond ((equal? sc0 0) 0)(else 1)))))
(define PreludeC-45EqOrd-u--C-62_Ord_Double (lambda (arg-0 arg-1) (let ((sc0 (or (and (> arg-0 arg-1) 1) 0))) (cond ((equal? sc0 0) 0)(else 1)))))
(define OptimizedPolynomial-clampCoeff (lambda (arg-0) (let ((sc0 (PreludeC-45EqOrd-u--C-62_Ord_Double arg-0 OptimizedPolynomial-MAX_COEFF))) (cond ((equal? sc0 1) OptimizedPolynomial-MAX_COEFF) (else (let ((sc1 (PreludeC-45EqOrd-u--C-60_Ord_Double arg-0 (- OptimizedPolynomial-MAX_COEFF)))) (cond ((equal? sc1 1) (- OptimizedPolynomial-MAX_COEFF)) (else arg-0))))))))
(define DataC-45Vect-foldrImpl (lambda (arg-3 arg-4 arg-5 arg-6) (if (null? arg-6) (arg-5 arg-4) (let ((e-3 (car arg-6))) (let ((e-4 (cdr arg-6))) (DataC-45Vect-foldrImpl arg-3 arg-4 (lambda (eta-0) (arg-5 ((arg-3 e-3) eta-0))) e-4))))))
(define DataC-45Vect-u--foldr_Foldable_C-40VectC-32C-36nC-41 (lambda (arg-3 arg-4 arg-5) (DataC-45Vect-foldrImpl arg-3 arg-4 (lambda (eta-0) eta-0) arg-5)))
(define DataC-45Vect-u--toList_Foldable_C-40VectC-32C-36nC-41 (lambda (ext-0) (DataC-45Vect-u--foldr_Foldable_C-40VectC-32C-36nC-41 (lambda (eta-0) (lambda (eta-1) (cons eta-0 eta-1))) '() ext-0)))
(define OptimizedPolynomial-evalPoly (lambda (arg-1 arg-2) (OptimizedPolynomial-n--5461-5632-u--evalPolyHelper arg-1 arg-2 (DataC-45Vect-u--toList_Foldable_C-40VectC-32C-36nC-41 arg-1) arg-2 0)))
(define DataC-45Vect-replicate (lambda (arg-1 arg-2) (cond ((equal? arg-1 0) '())(else (let ((e-0 (- arg-1 1))) (cons arg-2 (DataC-45Vect-replicate e-0 arg-2)))))))
(define PreludeC-45Types-u--foldl_Foldable_List (lambda (arg-2 arg-3 arg-4) (if (null? arg-4) arg-3 (let ((e-2 (car arg-4))) (let ((e-3 (cdr arg-4))) (PreludeC-45Types-u--foldl_Foldable_List arg-2 ((arg-2 arg-3) e-2) e-3))))))
(define OptimizedPolynomial-computeGradientOpt (lambda (arg-1 arg-2 arg-3 arg-4) (cond ((equal? arg-4 0) (let ((u--totalGrad (PreludeC-45Types-u--foldl_Foldable_List (lambda (u--acc) (lambda (lamc-0) (let ((e-2 (car lamc-0))) (let ((e-3 (cdr lamc-0))) (+ u--acc (/ (* 2.0 (- (OptimizedPolynomial-evalPoly arg-1 e-2) e-3)) arg-3)))))) 0.0 arg-2))) (cons u--totalGrad '())))(else (let ((e-0 (- arg-4 1))) (cond ((equal? e-0 0) (let ((u--grad0 (PreludeC-45Types-u--foldl_Foldable_List (lambda (u--acc) (lambda (lamc-0) (let ((e-2 (car lamc-0))) (let ((e-3 (cdr lamc-0))) (+ u--acc (/ (* 2.0 (- (OptimizedPolynomial-evalPoly arg-1 e-2) e-3)) arg-3)))))) 0.0 arg-2))) (let ((u--grad1 (PreludeC-45Types-u--foldl_Foldable_List (lambda (u--acc) (lambda (lamc-0) (let ((e-2 (car lamc-0))) (let ((e-3 (cdr lamc-0))) (+ u--acc (/ (* (* 2.0 (- (OptimizedPolynomial-evalPoly arg-1 e-2) e-3)) e-2) arg-3)))))) 0.0 arg-2))) (cons u--grad0 (cons u--grad1 '())))))(else (let ((e-1 (- e-0 1))) (cond ((equal? e-1 0) (let ((u--grad0 (PreludeC-45Types-u--foldl_Foldable_List (lambda (u--acc) (lambda (lamc-0) (let ((e-2 (car lamc-0))) (let ((e-3 (cdr lamc-0))) (+ u--acc (/ (* 2.0 (- (OptimizedPolynomial-evalPoly arg-1 e-2) e-3)) arg-3)))))) 0.0 arg-2))) (let ((u--grad1 (PreludeC-45Types-u--foldl_Foldable_List (lambda (u--acc) (lambda (lamc-0) (let ((e-2 (car lamc-0))) (let ((e-3 (cdr lamc-0))) (+ u--acc (/ (* (* 2.0 (- (OptimizedPolynomial-evalPoly arg-1 e-2) e-3)) e-2) arg-3)))))) 0.0 arg-2))) (let ((u--grad2 (PreludeC-45Types-u--foldl_Foldable_List (lambda (u--acc) (lambda (lamc-0) (let ((e-2 (car lamc-0))) (let ((e-3 (cdr lamc-0))) (+ u--acc (/ (* (* (* 2.0 (- (OptimizedPolynomial-evalPoly arg-1 e-2) e-3)) e-2) e-2) arg-3)))))) 0.0 arg-2))) (cons u--grad0 (cons u--grad1 (cons u--grad2 '())))))))(else (DataC-45Vect-replicate (+ (+ e-0 1) 1) 0.001)))))))))))
(define PreludeC-45TypesC-45List-lengthPlus (lambda (arg-1 arg-2) (if (null? arg-2) arg-1 (let ((e-3 (cdr arg-2))) (PreludeC-45TypesC-45List-lengthPlus (+ arg-1 1) e-3)))))
(define PreludeC-45TypesC-45List-lengthTR (lambda (ext-0) (PreludeC-45TypesC-45List-lengthPlus 0 ext-0)))
(define DataC-45Vect-u--zipWith_Zippable_C-40VectC-32C-36kC-41 (lambda (arg-4 arg-5 arg-6) (if (null? arg-5) '() (let ((e-3 (car arg-5))) (let ((e-4 (cdr arg-5))) (let ((e-8 (car arg-6))) (let ((e-9 (cdr arg-6))) (cons ((arg-4 e-3) e-8) (DataC-45Vect-u--zipWith_Zippable_C-40VectC-32C-36kC-41 arg-4 e-4 e-9)))))))))
(define OptimizedPolynomial-trainStepOpt (lambda (arg-0 arg-1 arg-2 arg-3) (let ((u--numSamples (exact->inexact (PreludeC-45TypesC-45List-lengthTR arg-2)))) (let ((u--gradient (OptimizedPolynomial-computeGradientOpt arg-1 arg-2 u--numSamples arg-0))) (let ((u--newCoeffs (DataC-45Vect-u--zipWith_Zippable_C-40VectC-32C-36kC-41 (lambda (u--c) (lambda (u--g) (OptimizedPolynomial-clampCoeff (- u--c (* arg-3 u--g))))) arg-1 u--gradient))) u--newCoeffs)))))
(define OptimizedPolynomial-LEARNING_RATE 0.01)
(define OptimizedPolynomial-MAX_ITERATIONS 200)
(define OptimizedPolynomial-TOLERANCE 1e-6)
(define Builtin-snd (lambda (arg-2) (let ((e-3 (cdr arg-2))) e-3)))
(define OptimizedPolynomial-computeMSE (lambda (arg-1 arg-2) (let ((u--predictions (PreludeC-45TypesC-45List-mapAppend '() (lambda (lamc-0) (let ((e-2 (car lamc-0))) (OptimizedPolynomial-evalPoly arg-1 e-2))) arg-2))) (let ((u--targets (PreludeC-45TypesC-45List-mapAppend '() (lambda (eta-0) (Builtin-snd eta-0)) arg-2))) (let ((u--errors (DataC-45List-u--zipWith_Zippable_List csegen-23 u--predictions u--targets))) (let ((u--squaredErrors (PreludeC-45TypesC-45List-mapAppend '() (lambda (u--e) (* u--e u--e)) u--errors))) (let ((u--totalError (PreludeC-45Types-u--foldl_Foldable_List csegen-22 0.0 u--squaredErrors))) (let ((u--n (exact->inexact (PreludeC-45TypesC-45List-lengthTR arg-2)))) (let ((sc0 (PreludeC-45EqOrd-u--C-62_Ord_Double u--n 0.0))) (cond ((equal? sc0 1) (/ u--totalError u--n)) (else 0.0)))))))))))
(define PreludeC-45Types-prim__integerToNat (lambda (arg-0) (let ((sc0 (or (and (<= 0 arg-0) 1) 0))) (cond ((equal? sc0 0) 0)(else arg-0)))))
(define OptimizedPolynomial-trainPolyOpt (lambda (arg-0 arg-1 arg-2 arg-3) (cond ((equal? arg-3 0) (cons arg-1 (cons 0 0)))(else (let ((e-0 (- arg-3 1))) (let ((u--currentLoss (OptimizedPolynomial-computeMSE arg-1 arg-2))) (let ((u--updatedPoly (OptimizedPolynomial-trainStepOpt arg-0 arg-1 arg-2 OptimizedPolynomial-LEARNING_RATE))) (let ((u--newLoss (OptimizedPolynomial-computeMSE u--updatedPoly arg-2))) (let ((u--lossImprovement (- u--currentLoss u--newLoss))) (let ((u--relativeImprovement (let ((sc0 (PreludeC-45EqOrd-u--C-62_Ord_Double u--currentLoss 0.0))) (cond ((equal? sc0 1) (/ u--lossImprovement u--currentLoss)) (else 0.0))))) (let ((sc0 (let ((sc1 (PreludeC-45EqOrd-u--C-60_Ord_Double u--lossImprovement OptimizedPolynomial-TOLERANCE))) (cond ((equal? sc1 1) (PreludeC-45EqOrd-u--C-60_Ord_Double u--relativeImprovement 0.001)) (else 0))))) (cond ((equal? sc0 1) (cons arg-1 (cons (PreludeC-45Types-prim__integerToNat (- OptimizedPolynomial-MAX_ITERATIONS (+ e-0 1))) 1))) (else (let ((sc1 (PreludeC-45EqOrd-u--C-62_Ord_Double u--newLoss (* u--currentLoss 1.5)))) (cond ((equal? sc1 1) (cons arg-1 (cons (PreludeC-45Types-prim__integerToNat (- OptimizedPolynomial-MAX_ITERATIONS (+ e-0 1))) 0))) (else (OptimizedPolynomial-trainPolyOpt arg-0 u--updatedPoly arg-2 e-0)))))))))))))))))
(define OptimizedPolynomial-computeR2Opt (lambda (arg-1 arg-2) (let ((u--predictions (PreludeC-45TypesC-45List-mapAppend '() (lambda (lamc-0) (let ((e-2 (car lamc-0))) (OptimizedPolynomial-evalPoly arg-1 e-2))) arg-2))) (let ((u--targets (PreludeC-45TypesC-45List-mapAppend '() (lambda (eta-0) (Builtin-snd eta-0)) arg-2))) (let ((u--meanTarget (/ (PreludeC-45Types-u--foldl_Foldable_List csegen-22 0.0 u--targets) (exact->inexact (PreludeC-45TypesC-45List-lengthTR u--targets))))) (let ((u--totalSumSquares (PreludeC-45Types-u--foldl_Foldable_List csegen-22 0.0 (PreludeC-45TypesC-45List-mapAppend '() (lambda (u--y) (* (- u--y u--meanTarget) (- u--y u--meanTarget))) u--targets)))) (let ((u--residuals (DataC-45List-u--zipWith_Zippable_List csegen-23 u--targets u--predictions))) (let ((u--residualSumSquares (PreludeC-45Types-u--foldl_Foldable_List csegen-22 0.0 (PreludeC-45TypesC-45List-mapAppend '() (lambda (u--r) (* u--r u--r)) u--residuals)))) (let ((sc0 (PreludeC-45EqOrd-u--C-62_Ord_Double u--totalSumSquares 0.0))) (cond ((equal? sc0 1) (- 1.0 (/ u--residualSumSquares u--totalSumSquares))) (else 0.0)))))))))))
(define PreludeC-45InterfacesC-45BoolC-45Semigroup-u--C-60C-43C-62_Semigroup_AllBool (lambda (arg-0 arg-1) (cond ((equal? arg-0 1) arg-1) (else 0))))
(define PreludeC-45EqOrd-u--C-60C-61_Ord_Double (lambda (arg-0 arg-1) (let ((sc0 (or (and (<= arg-0 arg-1) 1) 0))) (cond ((equal? sc0 0) 0)(else 1)))))
(define PreludeC-45Num-u--abs_Abs_Double (lambda (arg-0) (let ((sc0 (PreludeC-45EqOrd-u--C-60_Ord_Double arg-0 (exact->inexact 0)))) (cond ((equal? sc0 1) (- arg-0)) (else arg-0)))))
(define PreludeC-45Types-u--foldMap_Foldable_List (lambda (arg-2 arg-3 ext-0) (PreludeC-45Types-u--foldl_Foldable_List (lambda (u--acc) (lambda (u--elem) (let ((e-1 (car arg-2))) ((e-1 u--acc) (arg-3 u--elem))))) (let ((e-2 (cdr arg-2))) e-2) ext-0)))
(define PreludeC-45EqOrd-u--max_Ord_Double (lambda (arg-0 arg-1) (let ((sc0 (PreludeC-45EqOrd-u--C-62_Ord_Double arg-0 arg-1))) (cond ((equal? sc0 1) arg-0) (else arg-1)))))
(define OptimizedPolynomial-computeOptMetrics (lambda (arg-0 arg-1 arg-2 arg-3 arg-4 arg-5) (let ((u--trainMSE (OptimizedPolynomial-computeMSE arg-1 arg-2))) (let ((u--testMSE (OptimizedPolynomial-computeMSE arg-1 arg-3))) (let ((u--trainR2 (OptimizedPolynomial-computeR2Opt arg-1 arg-2))) (let ((u--testR2 (OptimizedPolynomial-computeR2Opt arg-1 arg-3))) (let ((u--coeffsList (DataC-45Vect-u--toList_Foldable_C-40VectC-32C-36nC-41 arg-1))) (let ((u--maxCoeff (PreludeC-45Types-u--foldl_Foldable_List (lambda (eta-0) (lambda (eta-1) (PreludeC-45EqOrd-u--max_Ord_Double eta-0 eta-1))) 0.0 (PreludeC-45TypesC-45List-mapAppend '() (lambda (eta-0) (PreludeC-45Num-u--abs_Abs_Double eta-0)) u--coeffsList)))) (let ((u--boundsCompliant (PreludeC-45Types-u--foldMap_Foldable_List (cons (lambda (arg-8474) (lambda (arg-8477) (PreludeC-45InterfacesC-45BoolC-45Semigroup-u--C-60C-43C-62_Semigroup_AllBool arg-8474 arg-8477))) 1) (lambda (u--c) (PreludeC-45EqOrd-u--C-60C-61_Ord_Double (PreludeC-45Num-u--abs_Abs_Double u--c) OptimizedPolynomial-MAX_COEFF)) u--coeffsList))) (vector u--trainMSE u--testMSE u--trainR2 u--testR2 u--maxCoeff u--boundsCompliant arg-4 arg-5))))))))))
(define OptimizedPolynomial-initPolyOpt (lambda (arg-0) (DataC-45Vect-replicate (+ arg-0 1) 0.01)))
(define DataC-45List-isPrefixOfBy (lambda (arg-2 arg-3 arg-4) (if (null? arg-3) 1 (if (null? arg-4) 0 (let ((e-1 (car arg-3))) (let ((e-2 (cdr arg-3))) (let ((e-4 (car arg-4))) (let ((e-5 (cdr arg-4))) (let ((sc4 ((arg-2 e-1) e-4))) (cond ((equal? sc4 1) (DataC-45List-isPrefixOfBy arg-2 e-2 e-5)) (else 0)))))))))))
(define DataC-45List-tails (lambda (arg-1) (cons arg-1 (if (null? arg-1) '() (let ((e-3 (cdr arg-1))) (DataC-45List-tails e-3))))))
(define PreludeC-45InterfacesC-45BoolC-45Semigroup-u--C-60C-43C-62_Semigroup_AnyBool (lambda (arg-0 arg-1) (cond ((equal? arg-0 1) 1) (else arg-1))))
(define DataC-45List-isInfixOfBy (lambda (arg-2 arg-3 arg-4) (PreludeC-45Types-u--foldMap_Foldable_List (cons (lambda (arg-8474) (lambda (arg-8477) (PreludeC-45InterfacesC-45BoolC-45Semigroup-u--C-60C-43C-62_Semigroup_AnyBool arg-8474 arg-8477))) 0) (lambda (eta-0) (DataC-45List-isPrefixOfBy arg-2 arg-3 eta-0)) (DataC-45List-tails arg-4))))
(define DataC-45List-isInfixOf (lambda (arg-1 ext-0 ext-1) (DataC-45List-isInfixOfBy (lambda (eta-0) (lambda (eta-1) (let ((e-1 (car arg-1))) ((e-1 eta-0) eta-1)))) ext-0 ext-1)))
(define PreludeC-45EqOrd-u--C-61C-61_Eq_Char (lambda (arg-0 arg-1) (let ((sc0 (or (and (char=? arg-0 arg-1) 1) 0))) (cond ((equal? sc0 0) 0)(else 1)))))
(define PreludeC-45EqOrd-u--C-47C-61_Eq_Char (lambda (arg-0 arg-1) (let ((sc0 (PreludeC-45EqOrd-u--C-61C-61_Eq_Char arg-0 arg-1))) (cond ((equal? sc0 1) 0) (else 1)))))
(define DataC-45String-isInfixOf (lambda (arg-0 arg-1) (DataC-45List-isInfixOf (cons (lambda (arg-676) (lambda (arg-679) (PreludeC-45EqOrd-u--C-61C-61_Eq_Char arg-676 arg-679))) (lambda (arg-686) (lambda (arg-689) (PreludeC-45EqOrd-u--C-47C-61_Eq_Char arg-686 arg-689)))) (PreludeC-45Types-fastUnpack arg-0) (PreludeC-45Types-fastUnpack arg-1))))
(define OptimizedPolynomial-loadDatasetOpt (lambda (arg-0 ext-0) (let ((act-1 (PreludeC-45IO-prim__putStr (string-append (string-append "Loading optimized dataset: " arg-0) "\xa;") ext-0))) (((let ((u--syntheticData (let ((sc0 (DataC-45String-isInfixOf "clean" arg-0))) (cond ((equal? sc0 1) (OptimizedPolynomial-n--6897-6946-u--generateCleanDataOpt arg-0)) (else (let ((sc1 (DataC-45String-isInfixOf "outliers" arg-0))) (cond ((equal? sc1 1) (OptimizedPolynomial-n--6897-6947-u--generateOutliersDataOpt arg-0)) (else (OptimizedPolynomial-n--6897-6948-u--generatePathologicalDataOpt arg-0))))))))) (lambda () (lambda (eta-0) (box u--syntheticData))))) ext-0))))
(define PreludeC-45Show-u--show_Show_Bool (lambda (arg-0) (cond ((equal? arg-0 1) "True") (else "False"))))
(define PreludeC-45Show-firstCharIs (lambda (arg-0 arg-1) (cond ((equal? arg-1 "") 0)(else (arg-0 (string-ref arg-1 0))))))
(define PreludeC-45Show-showParens (lambda (arg-0 arg-1) (cond ((equal? arg-0 0) arg-1) (else (string-append "(" (string-append arg-1 ")"))))))
(define PreludeC-45EqOrd-u--C-61C-61_Eq_Ordering (lambda (arg-0 arg-1) (cond ((equal? arg-0 0) (cond ((equal? arg-1 0) 1)(else 0))) ((equal? arg-0 1) (cond ((equal? arg-1 1) 1)(else 0))) ((equal? arg-0 2) (cond ((equal? arg-1 2) 1)(else 0)))(else 0))))
(define PreludeC-45EqOrd-u--C-47C-61_Eq_Ordering (lambda (arg-0 arg-1) (let ((sc0 (PreludeC-45EqOrd-u--C-61C-61_Eq_Ordering arg-0 arg-1))) (cond ((equal? sc0 1) 0) (else 1)))))
(define PreludeC-45Show-precCon (lambda (arg-0) (case (vector-ref arg-0 0) ((0) 0) ((1) 1) ((2) 2) ((3) 3) ((4) 4) ((5) 5) (else 6))))
(define PreludeC-45EqOrd-u--C-60_Ord_Integer (lambda (arg-0 arg-1) (let ((sc0 (or (and (< arg-0 arg-1) 1) 0))) (cond ((equal? sc0 0) 0)(else 1)))))
(define PreludeC-45EqOrd-u--C-61C-61_Eq_Integer (lambda (arg-0 arg-1) (let ((sc0 (or (and (= arg-0 arg-1) 1) 0))) (cond ((equal? sc0 0) 0)(else 1)))))
(define PreludeC-45EqOrd-u--compare_Ord_Integer (lambda (arg-0 arg-1) (let ((sc0 (PreludeC-45EqOrd-u--C-60_Ord_Integer arg-0 arg-1))) (cond ((equal? sc0 1) 0) (else (let ((sc1 (PreludeC-45EqOrd-u--C-61C-61_Eq_Integer arg-0 arg-1))) (cond ((equal? sc1 1) 1) (else 2))))))))
(define PreludeC-45Show-u--compare_Ord_Prec (lambda (arg-0 arg-1) (case (vector-ref arg-0 0) ((4) (let ((e-0 (vector-ref arg-0 1))) (case (vector-ref arg-1 0) ((4) (let ((e-1 (vector-ref arg-1 1))) (PreludeC-45EqOrd-u--compare_Ord_Integer e-0 e-1)))(else (PreludeC-45EqOrd-u--compare_Ord_Integer (PreludeC-45Show-precCon arg-0) (PreludeC-45Show-precCon arg-1))))))(else (PreludeC-45EqOrd-u--compare_Ord_Integer (PreludeC-45Show-precCon arg-0) (PreludeC-45Show-precCon arg-1))))))
(define PreludeC-45Show-u--C-62C-61_Ord_Prec (lambda (arg-0 arg-1) (PreludeC-45EqOrd-u--C-47C-61_Eq_Ordering (PreludeC-45Show-u--compare_Ord_Prec arg-0 arg-1) 0)))
(define PreludeC-45Show-primNumShow (lambda (arg-1 arg-2 arg-3) (let ((u--str (arg-1 arg-3))) (PreludeC-45Show-showParens (let ((sc0 (PreludeC-45Show-u--C-62C-61_Ord_Prec arg-2 (vector 5 )))) (cond ((equal? sc0 1) (PreludeC-45Show-firstCharIs (lambda (arg-0) (PreludeC-45EqOrd-u--C-61C-61_Eq_Char arg-0 #\-)) u--str)) (else 0))) u--str))))
(define PreludeC-45Show-u--showPrec_Show_Double (lambda (ext-0 ext-1) (PreludeC-45Show-primNumShow (lambda (eta-0) (number->string eta-0)) ext-0 ext-1)))
(define PreludeC-45Show-u--show_Show_Double (lambda (arg-0) (PreludeC-45Show-u--showPrec_Show_Double (vector 0 ) arg-0)))
(define PreludeC-45Show-u--showPrec_Show_Integer (lambda (ext-0 ext-1) (PreludeC-45Show-primNumShow (lambda (eta-0) (number->string eta-0)) ext-0 ext-1)))
(define PreludeC-45Show-u--show_Show_Integer (lambda (arg-0) (PreludeC-45Show-u--showPrec_Show_Integer (vector 0 ) arg-0)))
(define PreludeC-45Show-u--show_Show_Nat (lambda (arg-0) (PreludeC-45Show-u--show_Show_Integer arg-0)))
(define OptimizedPolynomial-runOptimizedExperiment (lambda (arg-0 arg-1 ext-0) (let ((act-1 (PreludeC-45IO-prim__putStr (string-append (string-append "Running optimized experiment: " (string-append arg-0 (string-append " (degree " (string-append (PreludeC-45Show-u--show_Show_Nat arg-1) ")")))) "\xa;") ext-0))) (let ((act-2 (OptimizedPolynomial-loadDatasetOpt (string-append "datasets/" (string-append arg-0 "_train.csv")) ext-0))) (if (null? act-2) (PreludeC-45IO-prim__putStr "Failed to load training data\xa;" ext-0) (let ((e-1 (unbox act-2))) (let ((act-3 (OptimizedPolynomial-loadDatasetOpt (string-append "datasets/" (string-append arg-0 "_test.csv")) ext-0))) (if (null? act-3) (PreludeC-45IO-prim__putStr "Failed to load test data\xa;" ext-0) (let ((e-2 (unbox act-3))) (let ((u--initialPoly (OptimizedPolynomial-initPolyOpt arg-1))) (let ((sc2 (OptimizedPolynomial-trainPolyOpt arg-1 u--initialPoly e-1 OptimizedPolynomial-MAX_ITERATIONS))) (let ((e-4 (car sc2))) (let ((e-3 (cdr sc2))) (let ((e-6 (car e-3))) (let ((e-7 (cdr e-3))) (let ((u--metrics (OptimizedPolynomial-computeOptMetrics arg-1 e-4 e-1 e-2 e-6 e-7))) (let ((act-4 (PreludeC-45IO-prim__putStr (string-append (string-append "Results for " (string-append arg-0 ":")) "\xa;") ext-0))) (let ((act-5 (PreludeC-45IO-prim__putStr (string-append (string-append "  Train MSE: " (PreludeC-45Show-u--show_Show_Double (let ((e-0 (vector-ref u--metrics 0))) e-0))) "\xa;") ext-0))) (let ((act-6 (PreludeC-45IO-prim__putStr (string-append (string-append "  Test MSE: " (PreludeC-45Show-u--show_Show_Double (let ((e-13 (vector-ref u--metrics 1))) e-13))) "\xa;") ext-0))) (let ((act-7 (PreludeC-45IO-prim__putStr (string-append (string-append "  Train R\xb2;: " (PreludeC-45Show-u--show_Show_Double (let ((e-12 (vector-ref u--metrics 2))) e-12))) "\xa;") ext-0))) (let ((act-8 (PreludeC-45IO-prim__putStr (string-append (string-append "  Test R\xb2;: " (PreludeC-45Show-u--show_Show_Double (let ((e-11 (vector-ref u--metrics 3))) e-11))) "\xa;") ext-0))) (let ((act-9 (PreludeC-45IO-prim__putStr (string-append (string-append "  Max |Coeff|: " (PreludeC-45Show-u--show_Show_Double (let ((e-10 (vector-ref u--metrics 4))) e-10))) "\xa;") ext-0))) (let ((act-10 (PreludeC-45IO-prim__putStr (string-append (string-append "  Bounds Compliant: " (PreludeC-45Show-u--show_Show_Bool (let ((e-5 (vector-ref u--metrics 5))) e-5))) "\xa;") ext-0))) (let ((act-11 (PreludeC-45IO-prim__putStr (string-append (string-append "  Iterations: " (PreludeC-45Show-u--show_Show_Nat (let ((e-9 (vector-ref u--metrics 6))) e-9))) "\xa;") ext-0))) (let ((act-12 (PreludeC-45IO-prim__putStr (string-append (string-append "  Converged: " (PreludeC-45Show-u--show_Show_Bool (let ((e-8 (vector-ref u--metrics 7))) e-8))) "\xa;") ext-0))) (PreludeC-45IO-prim__putStr "\xa;" ext-0))))))))))))))))))))))))))
(define OptimizedPolynomial-main (lambda (ext-0) (let ((act-1 (PreludeC-45IO-prim__putStr "Optimized Idris2 Polynomial Regression\xa;" ext-0))) (let ((act-2 (PreludeC-45IO-prim__putStr "======================================\xa;" ext-0))) (let ((act-3 (PreludeC-45IO-prim__putStr "\xa;" ext-0))) (let ((act-4 (OptimizedPolynomial-runOptimizedExperiment "clean" 2 ext-0))) (let ((act-5 (OptimizedPolynomial-runOptimizedExperiment "outliers" 3 ext-0))) (let ((act-6 (OptimizedPolynomial-runOptimizedExperiment "pathological" 5 ext-0))) (PreludeC-45IO-prim__putStr "Optimized experiments completed!\xa;" ext-0)))))))))
(define PreludeC-45EqOrd-compareInteger (lambda (ext-0 ext-1) (PreludeC-45EqOrd-u--compare_Ord_Integer ext-0 ext-1)))
(define PrimIO-unsafeCreateWorld (lambda (arg-1) (arg-1 #f)))
(define PrimIO-unsafePerformIO (lambda (arg-1) (PrimIO-unsafeCreateWorld (lambda (u--w) (let ((eff-0 (arg-1 u--w))) eff-0)))))
(collect-request-handler (lambda () (collect) (blodwen-run-finalisers)))
(PrimIO-unsafePerformIO (lambda (eta-0) (OptimizedPolynomial-main eta-0)))
  (collect 4)
  (blodwen-run-finalisers)
  
  )