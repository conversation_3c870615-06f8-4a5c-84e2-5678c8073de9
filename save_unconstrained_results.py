#!/usr/bin/env python3
"""
Save Unconstrained Idris Results

This script extracts the results from the unconstrained Idris run and saves them
in the format expected by the comprehensive analysis.
"""

import subprocess
import re
from pathlib import Path

def run_unconstrained_and_save():
    """Run unconstrained Idris and save results"""
    print("Running unconstrained Idris experiment and saving results...")
    
    try:
        # Run the unconstrained implementation
        result = subprocess.run(
            ["./build/exec/unconstrained-polynomial"],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode != 0:
            print(f"Error running unconstrained implementation: {result.stderr}")
            return
        
        output = result.stdout
        print("Unconstrained Idris output captured")
        
        # Parse results for each dataset
        datasets = ["clean", "outliers", "pathological"]
        
        for dataset in datasets:
            # Find the section for this dataset
            pattern = f"Unconstrained Results for {dataset}:(.*?)(?=Unconstrained Results for|$)"
            match = re.search(pattern, output, re.DOTALL)
            
            if match:
                section = match.group(1)
                
                # Extract metrics
                metrics = {}
                
                # Extract values using regex - prioritize RMSE over MSE
                train_rmse_match = re.search(r"Train RMSE:\s*([\d.-]+)", section)
                test_rmse_match = re.search(r"Test RMSE:\s*([\d.-]+)", section)
                train_mse_match = re.search(r"Train MSE:\s*([\d.-]+)", section)
                test_mse_match = re.search(r"Test MSE:\s*([\d.-]+)", section)
                train_r2_match = re.search(r"Train R^2:\s*([\d.-]+)", section)
                test_r2_match = re.search(r"Test R^2:\s*([\d.-]+)", section)
                max_coeff_match = re.search(r"Max \|Coeff\|:\s*([\d.-]+)", section)
                exploded_match = re.search(r"Exploded:\s*(True|False)", section)
                iterations_match = re.search(r"Iterations:\s*(\d+)", section)
                converged_match = re.search(r"Converged:\s*(True|False)", section)
                
                # Prioritize RMSE, fall back to MSE converted to RMSE
                if train_rmse_match:
                    metrics['Train RMSE'] = float(train_rmse_match.group(1))
                elif train_mse_match:
                    metrics['Train RMSE'] = float(train_mse_match.group(1)) ** 0.5

                if test_rmse_match:
                    metrics['Test RMSE'] = float(test_rmse_match.group(1))
                elif test_mse_match:
                    metrics['Test RMSE'] = float(test_mse_match.group(1)) ** 0.5
                if train_r2_match:
                    metrics['Train R^2'] = float(train_r2_match.group(1))
                if test_r2_match:
                    metrics['Test R^2'] = float(test_r2_match.group(1))
                if max_coeff_match:
                    metrics['Max |Coeff|'] = float(max_coeff_match.group(1))
                if exploded_match:
                    metrics['Exploded'] = exploded_match.group(1) == 'True'
                if iterations_match:
                    metrics['Iterations'] = int(iterations_match.group(1))
                if converged_match:
                    metrics['Converged'] = converged_match.group(1) == 'True'
                
                # Determine bounds compliance (not exploded and max coeff <= 2.0)
                bounds_compliant = (not metrics.get('Exploded', True)) and (metrics.get('Max |Coeff|', 999) <= 2.0)
                metrics['Bounds Compliant'] = bounds_compliant
                
                # Save to file
                results_dir = Path("results")
                results_dir.mkdir(exist_ok=True)
                
                filename = f"results/idris_unconstrained_{dataset}_metrics.txt"
                with open(filename, 'w') as f:
                    f.write(f"Idris2 Unconstrained Results - {dataset.title()} Dataset\n")
                    for key, value in metrics.items():
                        f.write(f"{key}: {value}\n")
                
                print(f"Saved {dataset} results to {filename}")
                
                # Also save coefficients if available (extract from output)
                coeff_pattern = f"after {dataset}.*?Coeff L2 Norm:\s*([\d.-]+)"
                # For now, we'll skip coefficient extraction as it's not in the output format
                
        print("All unconstrained results saved successfully")
        
    except subprocess.TimeoutExpired:
        print("Unconstrained implementation timed out")
    except Exception as e:
        print(f"Error running unconstrained implementation: {e}")

if __name__ == "__main__":
    run_unconstrained_and_save()
