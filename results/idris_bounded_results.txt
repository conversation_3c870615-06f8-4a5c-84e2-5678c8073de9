 Type-Safe Polynomial Regression with Real Datasets
====================================================

 Processing clean dataset...
Loading dataset: datasets/clean_train.csv
Loaded 560 data points
Loading dataset: datasets/clean_test.csv
Loaded 240 data points
Loaded 560 training samples, 240 test samples
Training polynomial (degree 3) on clean dataset...
   Iteration 0: loss = 2.351374472899282
   Iteration 100: loss = 11.264616084122583
   Iteration 200: loss = 11.264616084122583
   Iteration 300: loss = 11.264616084122583
   Iteration 400: loss = 11.264616084122583
   Iteration 500: loss = 11.264616084122583
   Iteration 600: loss = 11.264616084122583
   Iteration 700: loss = 11.264616084122583
   Iteration 800: loss = 11.264616084122583
   Iteration 900: loss = 11.264616084122583
   Final loss after 1000 iterations: 11.264616084122583
Performance Metrics:
   Test RMSE: 10.606679566852184
   Test R²: -19.79378124106973
   Execution Time: 0.463997s
   Memory Usage: 1.548828125MB
 Max |coefficient|: 2.0
 Within bounds: True
 Bound compliance rate: 100.0%
 Results saved for clean dataset

 Processing outliers dataset...
Loading dataset: datasets/outliers_train.csv
Loaded 420 data points
Loading dataset: datasets/outliers_test.csv
Loaded 180 data points
Loaded 420 training samples, 180 test samples
Training polynomial (degree 3) on outliers dataset...
   Iteration 0: loss = 1.120290156019312
   Iteration 100: loss = 5.924826133249046
   Iteration 200: loss = 5.924826133249046
   Iteration 300: loss = 5.924826133249046
   Iteration 400: loss = 5.924826133249046
   Iteration 500: loss = 5.924826133249046
   Iteration 600: loss = 5.924826133249046
   Iteration 700: loss = 5.924826133249046
   Iteration 800: loss = 5.924826133249046
   Iteration 900: loss = 5.924826133249046
   Final loss after 1000 iterations: 5.924826133249046
Performance Metrics:
   Test RMSE: 6.704410536548486
   Test R²: -67.852513171239
   Execution Time: 0.353448s
   Memory Usage: 1.53662109375MB
 Max |coefficient|: 2.0
 Within bounds: True
 Bound compliance rate: 100.0%
 Results saved for outliers dataset

 Processing pathological dataset...
Loading dataset: datasets/pathological_train.csv
Loaded 280 data points
Loading dataset: datasets/pathological_test.csv
Loaded 120 data points
Loaded 280 training samples, 120 test samples
Training polynomial (degree 3) on pathological dataset...
   Iteration 0: loss = 0.6225983351438147
   Iteration 100: loss = 1.273015538818079
   Iteration 200: loss = 3.0530126334687364
   Iteration 300: loss = 3.870063896861335
   Iteration 400: loss = 3.870063896861335
   Iteration 500: loss = 3.870063896861335
   Iteration 600: loss = 3.870063896861335
   Iteration 700: loss = 3.870063896861335
   Iteration 800: loss = 3.870063896861335
   Iteration 900: loss = 3.870063896861335
   Final loss after 1000 iterations: 3.870063896861335
Performance Metrics:
   Test RMSE: 3.6125844932099054
   Test R²: -91.38949417145909
   Execution Time: 0.21174s
   Memory Usage: 1.5244140625MB
 Max |coefficient|: 2.0
 Within bounds: True
 Bound compliance rate: 100.0%
 Results saved for pathological dataset
Loading dataset: datasets/clean_test.csv
Loaded 240 data points
   Iteration 0: loss = 2.3151717068412982
   Iteration 100: loss = 10.606679566852184
   Iteration 200: loss = 10.606679566852184
   Iteration 300: loss = 10.606679566852184
   Iteration 400: loss = 10.606679566852184
   Iteration 500: loss = 10.606679566852184
   Iteration 600: loss = 10.606679566852184
   Iteration 700: loss = 10.606679566852184
   Iteration 800: loss = 10.606679566852184
   Iteration 900: loss = 10.606679566852184
   Final loss after 1000 iterations: 10.606679566852184

 Type-safe polynomial regression complete!
 All datasets processed with bounded coefficients
