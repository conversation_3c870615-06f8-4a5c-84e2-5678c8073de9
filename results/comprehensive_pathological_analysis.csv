Implementation,mse,rmse,mae,r2,train_r2,adjusted_r2,overfitting_gap,max_coeff_magnitude,coeff_l2_norm,bound_violation_rate,coeff_recovery_mse,bounds_compliant,median_ae,huber_loss,cv_r2_mean,cv_r2_std,execution_time,memory_usage,converged,iterations
Python+Manual Bounds,,,,0.5585951885927007,0.5146189011234829,,-0.04397628746921789,0.7553360806895855,0.9757688219043326,0.0,1.418076307319338,True,,,,,0.04637408256530762,0.0,False,1000.0
Python+Unconstrained,,,,0.7240486736320442,0.7502474310803062,,0.026198757448262056,2.6895010055952917,3.566105981963274,33.33333333333333,0.3042970450360269,False,,,,,0.0018448829650878906,0.00390625,True,1.0
Idris2+<PERSON><PERSON>r,,3.6125844932099054,,-91.38949417145909,,,,,,,,,,,,,0.5,0.1,True,
