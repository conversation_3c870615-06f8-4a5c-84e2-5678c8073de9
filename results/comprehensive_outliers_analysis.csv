Implementation,mse,rmse,mae,r2,train_r2,adjusted_r2,overfitting_gap,max_coeff_magnitude,coeff_l2_norm,bound_violation_rate,coeff_recovery_mse,bounds_compliant,median_ae,huber_loss,cv_r2_mean,cv_r2_std,execution_time,memory_usage,converged,iterations
Python+Manual Bounds,,,,0.9331573154669446,0.9149680794595677,,-0.018189236007376874,1.2440342418368768,1.8217896421158237,0.0,0.0231999331785319,True,,,,,0.049111127853393555,0.0234375,False,1000.0
Python+Unconstrained,,,,0.9439614714385558,0.928167578567735,,-0.01579389287082078,1.538902399397636,2.128784886184568,0.0,0.01060225362639363,True,,,,,0.0024619102478027344,0.00390625,True,1.0
Idris2+<PERSON>pidr,,6.704410536548486,,-67.852513171239,,,,,,,,,,,,,0.5,0.1,True,
