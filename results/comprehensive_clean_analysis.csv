Implementation,mse,rmse,mae,r2,train_r2,adjusted_r2,overfitting_gap,max_coeff_magnitude,coeff_l2_norm,bound_violation_rate,coeff_recovery_mse,bounds_compliant,median_ae,huber_loss,cv_r2_mean,cv_r2_std,execution_time,memory_usage,converged,iterations
Python+Manual Bounds,,,,0.9981051301251513,0.998295541329263,,0.00019041120411167345,1.7973971146170034,2.0693416615431466,0.0,8.841478207858246e-06,True,,,,,0.04227614402770996,0.22265625,True,831.0
Python+Unconstrained,,,,0.9981061532238656,0.9982957842251615,,0.00018963100129587573,1.7974103714593923,2.0092574999766897,0.0,0.08333688160500881,True,,,,,0.0036978721618652344,0.046875,True,1.0
Idris2+Spidr,,10.606679566852184,,-19.79378124106973,,,,,,,,,,,,,0.5,0.1,True,
