[{"model_name": "Python+Manual Bounds", "dataset_name": "clean", "degree": 2, "train_rmse": 0.09708132415945704, "test_rmse": 0.10125171991286856, "train_r2": 0.998295541329263, "test_r2": 0.9981051301251513, "execution_time": 0.04227614402770996, "peak_memory_mb": 0.22265625, "iterations": 831, "converged": true, "coefficients": [-0.496541014607171, 1.7973971146170034, 0.8972098669639689], "max_coefficient": 1.7973971146170034, "bounds_compliant": true, "bound_compliance_rate": 1.0, "gradient_norm": 0.0014127989188416838}, {"model_name": "Python+Unconstrained", "dataset_name": "clean", "degree": 2, "train_rmse": 0.09707440656854456, "test_rmse": 0.10122438175826913, "train_r2": 0.9982957842251615, "test_r2": 0.9981061532238656, "execution_time": 0.0036978721618652344, "peak_memory_mb": 0.046875, "iterations": 1, "converged": true, "coefficients": [0.0, 1.7974103714593923, 0.8980153995242991], "max_coefficient": 1.7974103714593923, "bounds_compliant": true, "bound_compliance_rate": 1.0, "gradient_norm": 0.0}, {"model_name": "Python+Manual Bounds", "dataset_name": "outliers", "degree": 3, "train_rmse": 0.25782994119149216, "test_rmse": 0.2088948067509587, "train_r2": 0.9149680794595677, "test_r2": 0.9331573154669446, "execution_time": 0.049111127853393555, "peak_memory_mb": 0.0234375, "iterations": 1000, "converged": false, "coefficients": [0.2017244506485887, -1.2440342418368768, 0.8116981877236805, 1.0352534005326748], "max_coefficient": 1.2440342418368768, "bounds_compliant": true, "bound_compliance_rate": 1.0, "gradient_norm": 0.05862735115353442}, {"model_name": "Python+Unconstrained", "dataset_name": "outliers", "degree": 3, "train_rmse": 0.2369750418877229, "test_rmse": 0.19126876912880963, "train_r2": 0.928167578567735, "test_r2": 0.9439614714385558, "execution_time": 0.0024619102478027344, "peak_memory_mb": 0.00390625, "iterations": 1, "converged": true, "coefficients": [0.0, -1.538902399397636, 0.8086614499909799, 1.228646066234914], "max_coefficient": 1.538902399397636, "bounds_compliant": true, "bound_compliance_rate": 1.0, "gradient_norm": 0.0}, {"model_name": "Python+Manual Bounds", "dataset_name": "pathological", "degree": 5, "train_rmse": 0.27542911646417334, "test_rmse": 0.24970385668936837, "train_r2": 0.5146189011234829, "test_r2": 0.5585951885927007, "execution_time": 0.04637408256530762, "peak_memory_mb": 0.0, "iterations": 1000, "converged": false, "coefficients": [0.7553360806895855, -0.08544570996710109, -0.49046096313671084, -0.004592634891265926, -0.2965731010051232, -0.21392189406709575], "max_coefficient": 0.7553360806895855, "bounds_compliant": true, "bound_compliance_rate": 1.0, "gradient_norm": 0.028121691675859803}, {"model_name": "Python+Unconstrained", "dataset_name": "pathological", "degree": 5, "train_rmse": 0.19757109219794752, "test_rmse": 0.19743446245934537, "train_r2": 0.7502474310803062, "test_r2": 0.7240486736320442, "execution_time": 0.0018448829650878906, "peak_memory_mb": 0.00390625, "iterations": 1, "converged": true, "coefficients": [0.0, -0.12590215230096824, -2.6895010055952917, 0.5688331742314734, 2.0769485204987097, -0.9113498376771194], "max_coefficient": 2.6895010055952917, "bounds_compliant": false, "bound_compliance_rate": 0.6666666666666666, "gradient_norm": 0.0}]